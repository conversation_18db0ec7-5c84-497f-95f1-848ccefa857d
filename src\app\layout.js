"use client";
import "./globals.scss";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/layout";
import { GoogleTagManager } from "@next/third-parties/google";
import { WhatsAppIcon } from "@/components/ui";
import { SITE_CONFIG, CONTACT_INFO } from "@/lib/constants";

export default function RootLayout({ children }) {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    logo: SITE_CONFIG.logo,
    contactPoint: {
      "@type": "ContactPoint",
      telephone: CONTACT_INFO.phones,
    },
    address: {
      "@type": "PostalAddress",
      streetAddress: CONTACT_INFO.address.street,
      addressLocality: CONTACT_INFO.address.city,
      addressCountry: CONTACT_INFO.address.country,
      postalCode: CONTACT_INFO.address.postalCode,
    },
    sameAs: [
      CONTACT_INFO.socialMedia.facebook,
      CONTACT_INFO.socialMedia.twitter,
      CONTACT_INFO.socialMedia.instagram,
      CONTACT_INFO.socialMedia.linkedin,
    ],
  };

  return (
    <html lang="en">
      <head>
        <meta
          name="google-site-verification"
          content="15qFVqhUkTNXJT7C35hrc4NXO5gZmlMdsNUU296HOVA"
        />
        <GoogleTagManager gtmId="GTM-N688WQ3" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      </head>
      <body>
        <Header />
        <WhatsAppIcon />
        {children}
        <Footer />
      </body>
    </html>
  );
}
