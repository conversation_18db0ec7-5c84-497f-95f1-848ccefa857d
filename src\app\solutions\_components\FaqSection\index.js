"use client";
import Image from "next/image";
import { Accordion, Col, Container, Row } from "react-bootstrap";

const FaqSection = ({ title, accordionData }) => {
  return (
    <section className="section bg-bc-light faq-sec">
      <Container>
        <div className="company-value-head">
          <h2 className="main-title">{title}</h2>
        </div>
        <Row className="gx-5 align-items-center">
          <Col lg={8} className="m-auto">
            <Accordion >
              {accordionData.map((item, index) => (
                <Accordion.Item eventKey={index.toString()} key={index}>
                  <Accordion.Header>{item.question}</Accordion.Header>
                  <Accordion.Body>{item.answer}</Accordion.Body>
                </Accordion.Item>
              ))}
            </Accordion>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default FaqSection;
