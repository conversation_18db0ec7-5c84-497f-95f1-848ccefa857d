import Image from "next/image";
import { <PERSON><PERSON>, Col, Container, Row } from "react-bootstrap";

const SolutionBanner = ({ bgImage, title, description }) => {
  return (
    <section
      className="industry-banner"
      style={{
        backgroundImage: `url(${bgImage?.src})`,
      }}
    >
      <Container>
        <Row className="align-items-center">
          <Col md={6}>
            <div className="industry-banner-details mb-5">
              <h2 className="main-title mb-3" dangerouslySetInnerHTML={{ __html: title }}></h2>
              <p dangerouslySetInnerHTML={{ __html: description }}/>
              <div className="btn-wrap mt-4">
                {/* eslint-disable react/no-unescaped-entities */}
                <Button className="btn-secondary me-3">
                  Let's talk to Sales
                </Button>
                {/* eslint-enable react/no-unescaped-entities */}
                <Button className="btn btn-outline">See Demo</Button>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default SolutionBanner;
