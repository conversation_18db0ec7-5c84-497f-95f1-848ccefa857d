"use client"
import { Tab, Nav, Col, Row, Container } from 'react-bootstrap';
import PositionContent from './components/positioncontent';
const Positions = () => {
    const tabs = [
        'Business Development Executive',
        'Business Development Manager',
        'Java Spring Boot Developer',
        'Business Development Manager',
        'Java Spring Boot Developer',
        'Business Development Executive'
    ];
    return (
        <section className='section career-position-wrap'>
            <Container>
            <h2 className='main-title mb-5 text-center'>Open Positions</h2>
                <div className='position-row justify-content-between align-items-center'>
                    <Tab.Container defaultActiveKey="tab0">
                        <Row>
                            <Col sm={5} className="d-flex flex-column">
                                <Nav variant="pills" className="flex-column">
                                    {tabs.map((tab, index) => (
                                        <Nav.Item key={index} className="nav-item-custom">
                                            <Nav.Link eventKey={`tab${index}`} className="nav-link-custom">
                                                {tab}
                                            </Nav.Link>
                                        </Nav.Item>
                                    ))}
                                </Nav>
                            </Col>
                            <Col sm={7}>
                                <Tab.Content>
                                    {tabs.map((tab, index) => (
                                        <Tab.Pane eventKey={`tab${index}`} key={index} className='position-tab-content'>
                                           <PositionContent  title={tab}/>
                                        </Tab.Pane>
                                    ))}
                                </Tab.Content>
                            </Col>
                        </Row>
                    </Tab.Container>
                </div>
            </Container>
        </section>
    );
};

export default Positions;
