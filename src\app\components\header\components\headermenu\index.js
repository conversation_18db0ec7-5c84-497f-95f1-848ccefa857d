"use client";
import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { Navbar, Nav, NavDropdown, Row, Col } from "react-bootstrap";

const HeaderMenu = ({ menuOpen, setMenuOpen }) => {
  const [activeDropdown, setActiveDropdown] = useState(null);
  const dropdownRef = useRef(null);

  const handleToggleDropdown = (dropdown) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setActiveDropdown(null);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownRef]);

  return (
    <Navbar expand="lg" className="header-nav">
      <div className="navbar-wrap">
        <Navbar.Toggle onClick={() => setMenuOpen(!menuOpen)}>
          <div className="menu-toggle">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </Navbar.Toggle>
        <div className={`menu-collapse ${menuOpen ? "opened" : ""}`}>
          <Nav className="me-auto" ref={dropdownRef}>
            <NavDropdown
              title={
                <div
                  className="d-flex align-items-center drp-item"
                  onClick={() => handleToggleDropdown("solutions")}
                >
                  Solutions
                  <span className="menu-dpdnarrow-wrap">
                    <Image
                      src="/images/icons/menu-dropdown.svg"
                      alt="Dropdown Icon"
                      width={11}
                      height={7}
                      className="dpdn-arrow"
                    />
                  </span>
                </div>
              }
              id="solutions-dropdown"
              className="click-dropdown"
              show={activeDropdown === "solutions"}
            >
              <div className="custom-dropdown">
                <Row className="no-wrap">
                  <Col>
                    <NavDropdown.Item as="div">
                      <ul className="header-submenu">
                        <li>
                          <Nav.Link href="/solutions/indoor-mapping">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/indoor-nav-icon.png"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Indoor Navigation
                          </Nav.Link>
                        </li>
                        <li>
                          <Nav.Link href="/solutions/proximity-services">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/proximity-icon.png"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Proximity services
                          </Nav.Link>
                        </li>
                        <li>
                          <Nav.Link href="/solutions/kiosk">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/kiosks-icon.png"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Kiosks
                          </Nav.Link>
                        </li>
                      </ul>
                    </NavDropdown.Item>
                  </Col>
                  <Col>
                    <NavDropdown.Item as="div">
                      <ul className="header-submenu">
                        <li>
                          <Nav.Link href="/solutions/asset-tracking">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/asset-tracking.png"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Asset Tracking
                          </Nav.Link>
                        </li>
                        <li>
                          <Nav.Link href="/solutions/analytics">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/data-analytics-icon.png"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Analytics
                          </Nav.Link>
                        </li>
                        <li>
                          <Nav.Link href="/solutions/web-app">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/webapp.png"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Web App
                          </Nav.Link>
                        </li>
                      </ul>
                    </NavDropdown.Item>
                  </Col>
                </Row>
              </div>
            </NavDropdown>

            <NavDropdown
              title={
                <div
                  className="d-flex align-items-center drp-item"
                  onClick={() => handleToggleDropdown("industries")}
                >
                  Industries
                  <span className="menu-dpdnarrow-wrap">
                    <Image
                      src="/images/icons/menu-dropdown.svg"
                      alt="Dropdown Icon"
                      width={11}
                      height={7}
                      className="dpdn-arrow"
                    />
                  </span>
                </div>
              }
              id="industries-dropdown"
              className="click-dropdown"
              show={activeDropdown === "industries"}
            >
              <div className="custom-dropdown">
                <Row className="no-wrap">
                  <Col>
                    <NavDropdown.Item as="div">
                      <ul className="header-submenu">
                        {/* <li>
                          <Nav.Link href="">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-cart-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Retail
                          </Nav.Link>
                        </li> */}
                        <li>
                          <Nav.Link href="/industry/shopping-malls-indoor-navigation">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-shoppinmall-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Shopping Malls
                          </Nav.Link>
                        </li>
                        <li>
                          <Nav.Link href="/industry/airport-indoor-navigation">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-flight-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Airports
                          </Nav.Link>
                        </li>
                        <li>
                          <Nav.Link href="/industry/schools-universities-indoor-navigation">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-school-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Schools & Universities
                          </Nav.Link>
                        </li>
                      </ul>
                    </NavDropdown.Item>
                  </Col>
                  <Col>
                    <NavDropdown.Item as="div">
                      <ul className="header-submenu">
                      <li>
                          <Nav.Link href="/industry/healthcare-indoor-navigation">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-healthcare-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Healthcare
                          </Nav.Link>
                        </li>
                        
                        <li>
                          <Nav.Link href="/industry/warehouses-indoor-navigation">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-warehouse-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Warehouses
                          </Nav.Link>
                        </li>
                       
                        {/* 
                        <li>
                          <Nav.Link href="">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-school-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Schools & Universities
                          </Nav.Link>
                        </li>
                        <li>
                          <Nav.Link href="">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-themepark-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Theme Parks
                          </Nav.Link>
                        </li>
                        <li>
                          <Nav.Link href="">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-events-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Events & Trade Shows
                          </Nav.Link>
                        </li> */}
                         <li>
                          <Nav.Link href="/industry/events-indoor-navigation">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-events-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Events & Trade Shows
                          </Nav.Link>
                        </li>
                      </ul>
                    </NavDropdown.Item>
                  </Col>
                  <Col>
                    <NavDropdown.Item as="div">
                      <ul className="header-submenu">
                       
                      <li>
                          <Nav.Link href="/industry/hotels-resorts-indoor-navigation">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-hotel-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Hotels & Resorts
                          </Nav.Link>
                        </li>
                        <li>
                          <Nav.Link href="/industry/museums-indoor-navigation">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-museums-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Museums
                          </Nav.Link>
                        </li>

                        <li>
                          <Nav.Link href="/industry/stadiums-theme-parks-indoor-navigation">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-stadium-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Stadiums & Theme Parks
                          </Nav.Link>
                        </li>

                        {/* 
                        <li>
                          <Nav.Link href="">
                            <span className="nav-icon">
                              <Image
                                src="/images/icons/h-hotel-icon.svg"
                                alt="menu_icon"
                                width={20}
                                height={20}
                              />
                            </span>
                            Hotels & Resorts
                          </Nav.Link>
                        </li> */}
                      </ul>
                    </NavDropdown.Item>
                  </Col>
                </Row>
              </div>
            </NavDropdown>

            {/* <Nav.Link href="">Developers</Nav.Link> */}
            {/* <Nav.Link href="">Resources</Nav.Link> */}
            {/* <Nav.Link href="">Partners</Nav.Link> */}
            <Nav.Link href="/company">Company</Nav.Link>
            <Nav.Link href="/blog">Blog</Nav.Link>
          </Nav>
        </div>
      </div>
    </Navbar>
  );
};

export default HeaderMenu;
