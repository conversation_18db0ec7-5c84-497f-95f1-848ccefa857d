import ClientSlider from "@/app/company/_components/companybanner/components/clients";
import IndustryBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
import IndustryExperience from "../_components/IndustryExperience";
import IndustrySolutions from "../_components/solutions";
import IndustryAccordion from "../_components/IndustryAccordion";
import IndustryServices from "../_components/IndustryServices";
import IndustryEBook from "../_components/IndustryEBook";
import IndustryHelp from "../_components/IndustryHelp";
import HeroImg from "@/app/assets/images/industry/healthcare-hero.png";
import SecImg from "@/app/assets/images/industry/health-sec-im.png";
import SecImg2 from "@/app/assets/images/industry/health-sec-im-2.png";
import SecImg3 from "@/app/assets/images/industry/health-help-illu.png";
import SecImg4 from "@/app/assets/images/industry/mall-im-sec-2.png";
import BgImg from "@/app/assets/images/industry/indu-accr-bg.png";

import TalkToOurTeam from "@/app/components/talktoourteam";
import FaqSection from "../_components/FaqSection";
import IndustrySecondSection from "../_components/IndustrySecondSection";

export const metadata = {
  metadataBase: new URL("https://becomap.com"),
  title: " Hospital wayfinding with indoor navigation solution | Becomap.",
  description:
    "Discover Hospital Wayfinding Solutions for Smarter Healthcare Navigation with Becomap. Improve patient experience and hospital efficiency with advanced mapping tech.",
  images: ["/becomap.png"],
  openGraph: {
    title: " Hospital wayfinding with indoor navigation solution | Becomap.",
    description:
      "Discover Hospital Wayfinding Solutions for Smarter Healthcare Navigation with Becomap. Improve patient experience and hospital efficiency with advanced mapping tech.",
    images: ["/becomap.png"],
  },
};

const hospitalNavigationFAQ = [
  {
    question: "How does indoor navigation work in hospitals?",
    answer:
      "We create detailed digital maps of your hospital and integrate them with our navigation software, allowing patients and visitors to easily find rooms, services, and other key areas.",
  },
  {
    question: "What are interactive kiosks?",
    answer:
      "Interactive kiosks are touch-screen systems located throughout the hospital that help visitors find locations and services easily. They reduce the burden on hospital staff and ensure visitors reach their destinations quickly.",
  },
  {
    question: "How does beComap benefit hospitals?",
    answer:
      "beComap reduces confusion for patients and visitors, improves hospital efficiency, and allows staff to focus on critical tasks rather than providing directions. It improves the overall hospital experience for everyone.",
  },
];

const SOLUTIONS = [
  {
    title: "Clear Directions",
    description:
      "beComap’s Indoor navigation offers clear, easy-to-follow directions for patients and visitors to find their way through the hospital. From rooms to pharmacies, visitors can navigate smoothly, reducing confusion and saving time.",
    iconUrl: "/images/icons/patient.png",
    color: "#00BEDA",
  },
  {
    title: "Make Essential Services Easy to Find",
    description:
      "In a busy hospital, services like emergency rooms, labs, and pharmacies are crucial. beComap lets you highlight these key areas so patients can reach them without delay. No more guessing, just clear directions to the care they need.",
    iconUrl: "/images/icons/coins.png",
    color: "#ED55B9",
  },
  {
    title: "Use Data to Improve Efficiency",
    description:
      "Understanding how patients and visitors move through your hospital can help you optimize operations. beComap’s data insights allow you to monitor foot traffic, improve your layout, and keep things running smoothly—all without intruding on user privacy.",
    iconUrl: "/images/icons/management.png",
    color: "#ED790F",
  },
];

const accordionData = [
  {
    title: "Easy-to-Use for Everyone",
    content:
      "Whether your visitors are looking for the nearest restroom or the pharmacy, beComap gives patients and visitors a clear route. Visitors can easily scan the QR code to get turn-by-turn directions, stress-free, while your staff focuses on patient care.",
  },
  {
    title: "Better Navigation, Better Reviews!",
    content:
      "With beComap, patients and visitors can easily navigate your hospital, finding the services they need without confusion. A smoother experience means happier visitors, which leads to better reviews and positive word-of-mouth—ultimately increasing the number of people choosing your hospital for their care.",
  },
  {
    title: "Better Navigation, Better Care, More Trust",
    content:
      "When patients and visitors struggle to navigate a hospital, it can lead to frustration and stress, often directed at your staff. Clear, easy-to-follow navigation helps eliminate these issues, making visits smoother. When people can find their way easily, they feel more comfortable, trust your hospital, and are more likely to return or recommend your services—leading to more visitors, positive reviews, and increased revenue.",
  },
  {
    title: "Connect Your Hospital’s Services",
    content:
      "beComap integrates directly with your hospital’s existing systems, making it easier for patients to navigate between departments. Whether they need to reach diagnostic centers or patient rooms, they’ll find their way quickly without asking for directions.",
  },
  {
    title: "Speak the Language of Your Patients",
    content:
      "Hospitals serve a diverse group of people. beComap offers multi-language support, so patients and visitors can get directions in the language they’re most comfortable with. This simple feature makes your hospital more welcoming and accessible to everyone.",
  },
  {
    title: "Keep Visitors Updated",
    content:
      "With beComap, visitors can easily stay updated on important hospital news, whether it’s changes to services, new facilities, or updated hours. No app is needed—just a quick scan of a QR code, and they have all the information. Hospital management can also send out news, announcements, and upcoming events, making it easy to keep everyone in the loop.",
  },
];

const ServiceData = [
  {
    title: "QR Code Scanning. No Downloads Needed",
    content:
      "beComap’s web-based solution means there are no app downloads required. Patients and visitors simply scan a QR code at the entrance to access step-by-step directions, making navigation effortless without additional hassle.",
    icon: "/images/icons/rfid.png",
    image: "/images/qr-code-scanning-health.png",
  },
  {
    title: "Parking Made Simple",
    content:
      "Finding a parking spot shouldn’t be another headache for visitors. beComap organizes hospital parking into zones with QR codes. Visitors scan the code when they park and are guided back to their vehicle when it’s time to leave, saving them from wandering around the lot.",
    icon: "/images/icons/parked-car.png",
    image: "/images/parking-made-simple.png",
  },
  {
    title: "Optimize Hospital Operations",
    content:
      "Managing a hospital involves a lot of moving parts. beComap helps you track equipment, manage space, and assign tasks efficiently, all from one simple dashboard. You’ll have everything you need to make your hospital run like a well-organized system, keeping both patients and staff happy.",
    icon: "/images/icons/pharmacy.png",
    image: "/images/optimize-hospital-operations.png",
  },
  {
    title: "Tailored Maps for Your Hospital’s Branding",
    content:
      "Customize your hospital’s map to reflect its branding with beComap’s flexible design options. From logos to color schemes, beComap ensures your hospital map is visually consistent with your identity while enhancing the patient experience.",
    icon: "/images/icons/map01.png",
    image: "/images/tailored-maps.png",
  },
];

const HealthCare = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <IndustryBanner
        source="healthcare"
        bgImage={HeroImg}
        title="Hospital Wayfinding Solutions for Smarter Healthcare Navigation"
        description="Optimize hospital wayfinding with advanced indoor navigation solutions, making it easier for patients, visitors, and staff to navigate complex hospital environments efficiently and accurately."
      />
      <IndustryClients />
      <IndustrySecondSection
        routeType="hospital"
        type={"healthcare"}
        imageItem={"/images/hosp-mob.png"}
        image={SecImg4}
        title={
          "No More Confusions. Guide Patients and Visitors Straight to Where They Need to Be"
        }
        description="Hospitals can be overwhelming—patients and visitors shouldn’t have to worry about finding their way. They’re here for care, not confusion. beComap’s smart indoor navigation takes the stress out of hospital visits by helping people easily find their rooms, pharmacies, or other services. With beComap’s simple web-based system, we make navigation easy, reduce confusion, and let your staff focus on what matters most—patient care."
      />
      <IndustrySolutions
        imageProperty={{
          width: 441,
          height: 505,
          top: "-160px",
          bottom: "0",
          justifyContentParent: "end",
          justifyContent: "end",
          backgroundColor: "gradient",
        }}
        image={SecImg}
        solutions={SOLUTIONS}
        title={"A Better Experience for Patients and Visitors"}
        description="No one should have to wander around a hospital in frustration. With beComap, patients and visitors can find exactly where they need to go, quickly and easily. Whether they’re headed to a treatment room or trying to find the pharmacy, beComap makes it easy to get there without any trouble, giving them one less thing to worry about."
      />
      <IndustryExperience
        image={SecImg2}
        title="Staff Tracking"
        description="Staff tracking in hospitals improves coordination of cleaning services, ensures efficient task assignment for wardens and auxiliary staff, and manages access to restricted areas. This approach helps hospitals run more smoothly and supports better patient care."
      />
      <IndustryAccordion
        title={"Faster Routes, Better Patient Care"}
        description={
          "Time is critical in a hospital setting. beComap’s indoor navigation solution shows patients and visitors the quickest paths to their destinations—whether it’s an appointment, a clinic, or an emergency room. This saves valuable time and keeps hospital operations flowing efficiently."
        }
        bgImage={BgImg}
        accordionData={accordionData}
        videoSrc={"/videos/health.mp4"}
      />
      <IndustryServices
        title="No More Guesswork for Patients, Just Peace of Mind "
        description="Imagine a patient trying to find their way to an appointment or a family member searching for a loved one’s room. beComap provides clear, accurate navigation to get them there without the added stress."
        serviceData={ServiceData}
      />
      <IndustryHelp
        title="Why Leading Hospitals Trust beComap"
        description="beComap is reshaping hospital navigation and operations by simplifying movement within large facilities and improving visitor experiences. Patients and visitors get the guidance they need, staff have more time for critical tasks, and operations run more smoothly. beComap is more than just navigation—it’s a complete solution for better hospital flow."
        image={SecImg3}
        points={[
          "Best-in-class indoor mapping for hospitals",
          "8 years of research in location intelligence",
          "Optimized for mobile, web, and kiosks",
          "Easy integration with existing hospital apps",
          "Web-based navigation without app downloads",
          "Multi-language support for diverse populations",
        ]}
      />

      <IndustryEBook
        image={"/images/health-bg.png"}
        title={"Health Care In Action"}
        description={`<p>Unlock indoor navigation potential with our eBook. </p> <p>Explore technologies, strategies, and best practices to enhance wayfinding, visitor experience, and operations.</p>`}
      />

      <TalkToOurTeam source={"healthcare"}/>
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={hospitalNavigationFAQ}
      />
    </div>
  );
};

export default HealthCare;
