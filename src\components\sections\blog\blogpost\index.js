import Image from "next/image";
import Link from "next/link";

// const Card = () => {
//     function truncate(str) {
//         return str.length > 100 ? str.substring(0, 100) + "..." : str;
//     }
//     return (
//         <article className="col-lg-4 col-md-6 pb-4 mb-3" key={key}
//             itemScope
//             itemType="http://schema.org/Article"
//         >
//             <Link href={`/blog/${item?.url}`} itemProp="url" className="bc-blog-card blog">
//                 <div className="bc-blog-card-ins">
//                     <div className="post-image-wrapper">
//                         {item?.image && <Image
//                             src={item?.image}
//                             alt={item?.title}
//                             fill sizes="100vw" />}
//                     </div>
//                     <div className="post-content-wrapper">
//                         <div className="post-text-wrapper">
//                             <div className="post-info">
//                                 <div className="info-text category">{item?.category?.title}</div>

//                                 <div className="info-divider"></div>
//                                 <div className="info-text">{item?.author?.name}</div>

//                             </div>
//                             <div className="post-title">
//                                 <h4 className="post-title-text">{item?.title}</h4>
//                             </div>
//                             <div className="post-description">{item?.description}</div>
//                         </div>
//                     </div>
//                 </div>
//             </Link>
//         </article>
//     );
// };

const BlogPost = ({ key, item, imageHeight }) => {
  return (
    <article
      className="blogpost"
      key={key}
      itemScope
      itemType="http://schema.org/Article"
    >
      <Link
        href={`/blog/${item?.url}`}
        itemProp="url"
        className="blogpost-ins"
      >
        <div className="blog-postimage-wrap">
          {item?.image && (
            <Image
              src={item?.image}
              alt="Blog"
              width="100"
              height={imageHeight ? imageHeight : "585"}
              style={{ width: "100%" }}
            />
          )}
        </div>
        <div className="blogpost-topic-info d-flex align-items-center">
          <span className="blogpost-topic">{item?.category?.title}</span>
          <span className="blogpost-author">{item?.author?.name}</span>
        </div>
        <div className="blog-post-description">
          <h1 className="blogpost-heading mb-1">{item?.title}</h1>
          <p className="blogpost-paragraph">{item?.description}</p>
        </div>
      </Link>
    </article>
  );
};

export default BlogPost;
