"use client";
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import Image from "next/image";
import { useRouter } from "next/navigation";

const serviceItems = [
  {
    title: "Indoor Navigation",
    description:
      "We specialize in providing indoor mapping services for complex built-up areas to aid in indoor navigation. Supported by the latest indoor positioning systems and indoor navigation technologies, our indoor mapping solutions are easy to use and are backed by unmatched customer support.",
    iconUrl: "/images/icons/indoor-nav-icon.png",
    imageUrl: "/images/indoor-nav-img.png",
  },
  {
    title: "Proximity services",
    description:
      "Our specially designed proximity services combine indoor analytics and thus help to deliver notifications, marketing campaigns and emergency directions through the indoor navigation solutions instantaneously to any customer within the facility.",
    iconUrl: "/images/icons/proximity-icon.png",
    imageUrl: "/images/proximity-img.png",
  },
  {
    title: "Asset Tracking",
    description:
      "Realizing that the amount of working equipment that needs to be traced in vast facilities is constantly growing, we have designed our indoor navigation software with an asset tracking system that allows for fast indoor tracking and tracing of every item installed in the facility.",
    iconUrl: "/images/icons/asset-tracking.png",
    imageUrl: "/images/asst-tracking-img.png",
  },
  {
    title: "Analytics",
    description:
      "We specialize in providing indoor mapping services for complex built-up areas to aid",
    iconUrl: "/images/icons/data-analytics-icon.png",
    imageUrl: "/images/analytics-img.png",
  },
  {
    title: "Kiosks",
    description:
      "We specialize in providing indoor mapping services for complex built-up areas to aid",
    iconUrl: "/images/icons/kiosks-icon.png",
    imageUrl: "/images/kiosks-img.png",
  },
  {
    title: "Web Application",
    description:
      "We specialize in providing indoor mapping services for complex built-up areas to aid",
    iconUrl: "/images/icons/webapp.png",
    imageUrl: "/images/webapp-img.png",
  },
];

const OurServices = () => {

  const router = useRouter();
  return (
    <div className="section our-service-section bg-bc-light">
      <Container fluid={"xl"}>
        <Row className="align-items-center justify-content-center mb-5">
          <Col xl={8}>
            <div className="section-head text-center">
              <h2 className="main-title mb-2">Our Services</h2>
              <p>
                At beComap, we provide powerful indoor navigation software and
                related location services that help businesses to enhance
                complex indoor environments.
              </p>
            </div>
          </Col>
        </Row>
        <Row>
          {/* First Column */}
          <Col lg={6}>
            <div className="service-item-box" style={{ minHeight: "730px" }}>
              <div className="service-item-box-topsec">
                <div className="service-icon-wrap">
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[0].iconUrl}
                      alt={serviceItems[0].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[0].title}</h3>
                <p>{serviceItems[0].description}</p>
              </div>
              <div className="service-item-box-footer img-leftalign-footer-big h-100">
                <div className="service-itembox-img-wrap">
                  <Image
                    src={serviceItems[0].imageUrl}
                    alt={serviceItems[0].title}
                    width={0}
                    height={0}
                    style={{ width: "100%", height: "auto" }}
                    className="service-box-img"
                  />
                </div>
              </div>
              <div className="srv-overlay">
                <div>
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[0].iconUrl}
                      alt={serviceItems[0].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[0].title}</h3>
                <p>{serviceItems[0].description}</p>
                <div className="btn-wrap">
                  <Button className="btn-secondary" onClick={() => router.push("/solutions/indoor-mapping")}>Read More</Button>
                </div>
              </div>
            </div>
          </Col>

          {/* Second Column */}
          <Col lg={6}>
            <div className="service-item-box " style={{ minHeight: "730px" }}>
              <div className="service-item-box-topsec">
                <div className="service-icon-wrap">
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[1].iconUrl}
                      alt={serviceItems[1].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[1].title}</h3>
                <p>{serviceItems[1].description}</p>
              </div>
              <div className="service-item-box-footer img-rightalign-footer-big">
                <div
                  className="service-itembox-img-wrap sec"
                >
                  <Image
                    src={serviceItems[1].imageUrl}
                    alt={serviceItems[1].title}
                    width={0}
                    height={0}
                    style={{ width: "100%", height: "auto" }}
                    className="service-box-img"
                  />
                </div>
              </div>
              <div className="srv-overlay">
                <div>
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[1].iconUrl}
                      alt={serviceItems[1].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[1].title}</h3>
                <p>{serviceItems[1].description}</p>
                <div className="btn-wrap">
                  <Button className="btn-secondary" onClick={() => router.push("/solutions/proximity-services")}>Read More</Button>
                </div>
              </div>
            </div>
          </Col>

          {/* Third Column (Full Width) */}
          <Col lg={12}>
            <div className="service-item-box service-item-split-box d-flex flex-md-row justify-content-between">
              <div className="service-item-box-topsec">
                <div className="service-icon-wrap">
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[2].iconUrl}
                      alt={serviceItems[2].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[2].title}</h3>
                <p>{serviceItems[2].description}</p>
              </div>
              <div className="service-item-box-footer img-rightalign-footer-big">
                <div className="service-itembox-img-wrap">
                  <Image
                    src={serviceItems[2].imageUrl}
                    alt={serviceItems[2].title}
                    width={0}
                    height={0}
                    style={{ width: "100%", height: "auto" }}
                    className="service-box-img"
                  />
                </div>
              </div>
              <div className="srv-overlay">
                <div>
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[2].iconUrl}
                      alt={serviceItems[2].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[2].title}</h3>
                <p>{serviceItems[2].description}</p>
                <div className="btn-wrap">
                  <Button className="btn-secondary" onClick={() => router.push("/solutions/asset-tracking")}>Read More</Button>
                </div>
              </div>
            </div>
          </Col>

          {/* Fourth Column */}
          <Col lg={4} className="service-three-box">
            <div className="service-item-box sm" >
              <div className="service-item-box-topsec">
                <div className="service-icon-wrap">
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[3].iconUrl}
                      alt={serviceItems[3].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[3].title}</h3>
                {/* <p>{serviceItems[3].description}</p> */}
              </div>
              <div className="service-item-box-footer img-rightalign-footer-small">
                <div className="service-itembox-img-wrap">
                  <Image
                    src={serviceItems[3].imageUrl}
                    alt={serviceItems[3].title}
                    width={0}
                    height={0}
                    style={{ width: "100%", height: "auto" }}
                    className="service-box-img"
                  />
                </div>
              </div>
              <div className="srv-overlay">
                <div>
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[3].iconUrl}
                      alt={serviceItems[3].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[3].title}</h3>
                <p>{serviceItems[3].description}</p>
                <div className="btn-wrap">
                  <Button className="btn-secondary" onClick={() => router.push("/solutions/analytics")}>Read More</Button>
                </div>
              </div>
            </div>
          </Col>

          {/* Fifth Column */}
          <Col lg={4} className="service-three-box">
            <div className="service-item-box sm" >
              <div className="service-item-box-topsec">
                <div className="service-icon-wrap">
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[4].iconUrl}
                      alt={serviceItems[4].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[4].title}</h3>
                {/* <p>{serviceItems[4].description}</p> */}
              </div>
              <div className="service-item-box-footer img-leftalign-footer-small">
                <div className="service-itembox-img-wrap">
                  <Image
                    src={serviceItems[4].imageUrl}
                    alt={serviceItems[4].title}
                    width={0}
                    height={0}
                    style={{ width: "100%", height: "auto" }}
                    className="service-box-img"
                  />
                </div>
              </div>
              <div className="srv-overlay">
                <div>
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[4].iconUrl}
                      alt={serviceItems[4].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[4].title}</h3>
                <p>{serviceItems[4].description}</p>
                <div className="btn-wrap">
                  <Button className="btn-secondary" onClick={() => router.push("/solutions/kiosk")}>Read More</Button>
                </div>
              </div>
            </div>
          </Col>
          <Col lg={4} className="service-three-box">
            <div className="service-item-box sm" >
              <div className="service-item-box-topsec">
                <div className="service-icon-wrap">
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[5].iconUrl}
                      alt={serviceItems[5].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[5].title}</h3>
                {/* <p>{serviceItems[5].description}</p> */}
              </div>
              <div className="service-item-box-footer img-rightalign-footer-small">
                <div className="service-itembox-img-wrap">
                  <Image
                    src={serviceItems[5].imageUrl}
                    alt={serviceItems[5].title}
                    width={0}
                    height={0}
                    style={{ width: "100%", height: "auto" }}
                    className="service-box-img"
                  />
                </div>
              </div>
              <div className="srv-overlay">
                <div>
                  <span className="srv-icon-bg">
                    <Image
                      src={serviceItems[5].iconUrl}
                      alt={serviceItems[5].title}
                      width={28}
                      height={28}
                      className="service-icon-img"
                    />
                  </span>
                </div>
                <h3 className="box-titles">{serviceItems[5].title}</h3>
                <p>{serviceItems[5].description}</p>
                <div className="btn-wrap">
                  <Button className="btn-secondary" onClick={() => router.push("/solutions/web-app")}>Read More</Button>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default OurServices;
