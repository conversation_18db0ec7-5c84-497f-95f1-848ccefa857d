.company-clientsay-box {
  background: $primary-gradient;
  box-shadow: 0px 5px 14px rgba(2, 40, 39, 0.37);
  padding: 60px 35px;
  border-radius: 20px;
}

.clientsayslider {
  .swiper-slide {
    padding: 25px 16px 15px;
    @media (min-width: 767px) {
      max-width: 475.75px;
    }
  }
}
.company-clientsay-box {
  @media (min-width: 767px) {
    max-width: 475.75px;
  }

  .client-say-content {
    min-height: 150px;
    color: #fff;
    margin-top: 15px;
  }
}

.client-say-section-wrap {
  background-color: $secondary-color;
  background-image: url("/images/client-say-bg.png");
  background-repeat: no-repeat;
  background-position: 5px 50px;
}

.company-clientsay-authorbox-left {
  width: 74px;
  height: 74px;
  background-color: #fff;
  border-radius: 50%;
  border: 3px solid #bdbdbd;
  object-fit: contain;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.paper-clip-top {
  position: absolute;
  top: -25px;
  right: 60px;
}
.company-clientsay-authorbox {
  align-items: center;
}
.company-clientsay-authorbox-right {
  padding-left: 10px;
  h5 {
    margin-bottom: 0;
    font-family: $font-bold;
    color: #fff;
  }
  p {
    margin-bottom: 0;
    color: rgba(149, 255, 177, 1);
    font-family: $font-bold;
  }
}
