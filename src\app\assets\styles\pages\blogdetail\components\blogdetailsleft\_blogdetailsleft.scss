.table-of-contents {
  border: 1px solid rgba(173, 169, 169, 1);
  border-radius: 10px;
  padding: 22px 19px 22px 22px;
  margin-bottom: 35px;
//   position: sticky;
//   top: 50px;
  background-color: #fff;
  h4 {
    font-size: 18px;
    font-family: $font-bold;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(217, 217, 217, 1);
  }
}
.tableof-content-list {
  margin: 0;
  padding-left: 11px;
//   max-height: 400px;
//   overflow: auto;
  li {
    // margin-top: 17px;
    font-size: 14px;
    font-family: $font-medium;
    // margin-bottom: 6px;
    line-height: 22px;
    cursor: pointer;
    div {
        padding: 8px 10px;
        border-radius: 8px;
      &:hover {
        background-color: rgba($primary-color, 0.2);
      }
    }
  }
}
.blog-social-box {
  display: flex;
  justify-content: space-between;
  padding-right: 15px;
}
.blog-social-box-wrap {
  h4 {
    font-size: 18px;
    font-family: $font-bold;
    line-height: 30px;
  }
}
.detail-ebook-box {
  .ebook-box-wrap {
    p {
      line-height: 25px;
    }
    h3 {
      font-size: 20px;
      line-height: 30px;
    }
    .ebook-img {
      width: 214px;
      height: 214px;
      margin: 0 auto 30px;
      img {
        width: auto;
        height: 202px;
      }
    }
  }
}
