"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import Link from "next/link";
import Image from "next/image";
import Container from "react-bootstrap/Container";
import { Autoplay } from "swiper/modules";
import { useEffect, useState } from "react";
const ClientSlider = () => {
  const [listOfImages, setListOfImages] = useState([]);

  useEffect(() => {
    const importAll = (r) => r.keys().map(r);
    const images = importAll(
      require.context(
        "/public/images/clientimages/",
        false,
        /\.(png|jpe?g|svg)$/
      )
    );
    setListOfImages(images);
  }, []);
  return (
    <div className="company-cleientslider-wrap">
      <Swiper
        slidesPerView={5}
        spaceBetween={30}
        className="clientslider"
        loop={true}
        autoplay={{
          delay: 2500,
          disableOnInteraction: false,
        }}
        modules={[Autoplay]}
      >
        {listOfImages.map((item) => (
          <SwiperSlide key={item.id}>
            <div>
              <Image src={item} alt={item.alt} width="145" height="81" />
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default ClientSlider;
