import ClientSlider from "@/app/company/_components/companybanner/components/clients";
import SolutionBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
// import IndustryExperience from "../_components/IndustryExperience";
// import IndustrySecondSection from "../_components/IndustrySecondSection";
import IndustrySolutions from "../_components/solutions";
// import IndustryAccordion from "../_components/IndustryAccordion";
import IndustryServices from "../_components/IndustryServices";
import IndustryEBook from "../_components/IndustryEBook";
import FaqSection from "../_components/FaqSection";
import SolutionWhyUs from "../_components/solutionWhyUs";
import HeroImg from "@/app/assets/images/solutions/webapp-hero.png";
import SecImg from "@/app/assets/images/solutions/becomap-care.png";
// import SecImg2 from "@/app/assets/images/industry/mall-sec-im-2.png";
// import SecImg3 from "@/app/assets/images/industry/mall-help-illu.png";
// import SecImg4 from "@/app/assets/images/industry/mall-im-sec-2-2.png";
// import BgImg from "@/app/assets/images/industry/indu-accr-bg.png";
import TalkToOurTeam from "@/app/components/talktoourteam";
import Blogs from "@/app/home/<USER>/blogs";
import { Col, Container, Row } from "react-bootstrap";
import Image from "next/image";

import SolutionWhyUsBg from "@/app/assets/images/solutions/why-bg.png";
import WhySubImage from "@/app/assets/images/solutions/webapp/why-sub-img.png";
import SolutionsServices from "../_components/IndustryServices";


export const metadata = {
  metadataBase: new URL("https://becomap.com"),
  title: " Web App Solutions for Seamless Indoor maps | Becomap",
  description:
    "Explore Becomap's web app solutions, featuring advanced indoor maps for seamless navigation and enhanced user experiences in any environment.",
  images: ["/becomap.png"],
  openGraph: {
    title: " Web App Solutions for Seamless Indoor maps | Becomap",
    description:
      "Explore Becomap's web app solutions, featuring advanced indoor maps for seamless navigation and enhanced user experiences in any environment.",
    images: ["/becomap.png"],
  },
};



const mallNavigationFAQ = [
  {
    question: "Can the web app be accessed on any device?",
    answer: "Yes, the web app is compatible with most devices, including smartphones, tablets, and desktops, ensuring universal accessibility."
  },
  {
    question: "How secure is the data collected by the indoor navigation system?",
    answer: "The system uses encryption and secure data storage, ensuring compliance with privacy regulations and protecting user information."
  },
  {
    question: "Can the web app integrate with my existing systems and software?",
    answer: "Yes, we ensure seamless integration with your current systems, such as CRMs, ERPs, or third-party services, to provide a unified experience."
  },
  {
    question: "What kind of maintenance is required for the web app after launch?",
    answer: "Maintenance includes software updates, bug fixes, performance monitoring, and ensuring compatibility with new browser versions. We offer ongoing support to keep your web app running smoothly."
  }
];

const SOLUTIONS = [
  {
    title: "Interactive Maps",
    description:
      "Users can interact with digital maps, zooming and panning to explore the entire venue. This makes it easy for visitors to understand the layout and find their way around.",
    iconUrl: "/images/icons/resp.png",
    color: "#34A853",
  },
  {
    title: "No Download Required",
    description:
      "The Web App works directly through the browser, allowing visitors to access maps by scanning a QR code or following a web link. This means no app downloads are needed, providing quick access to navigation.",
    iconUrl: "/images/icons/cloud.png",
    color: "#34A853",
  },
  {
    title: "Cross-Device Compatibility",
    description:
      "The Web App is accessible on mobile phones, tablets, and desktops without any compatibility issues, ensuring a consistent experience for all visitors.",
    iconUrl: "/images/icons/map_5.png",
    color: "#34A853",
  },
];

const accordionData = [
  {
    title: "Easily Integrate Maps",
    content:
      "Integrate map of your retail space into existing apps to effortlessly guide shoppers to their favorite stores, restaurants, and services via the shortest route.",
  },
  {
    title: "Multi-Language Support",
    content:
      "Make your mall map accessible to all visitors by offering it in multiple languages. beComap allows you to provide directions and information in the shopper’s preferred language, making the mall experience more personalized and user-friendly. This feature is particularly valuable in diverse or tourist-heavy locations.",
  },
  {
    title: "Give Updates on Store Offers",
    content:
      "Keep shoppers in the loop with instant updates on store deals, upcoming events, and essential services, improving their overall shopping experience.",
  },
  {
    title: "Less Searching, More Shopping",
    content:
      "Navigate in a flash with our system—find the fastest route and shortest path from parking to stores, or multi-stop routes to plan your shopping with less walking and more items on your cart.",
  },
];

const ServiceData = [
  {
    title: "1. Choose the Right Signal Type",
    content:
      "Enhance your Web App experience with Bluetooth beacons that provide visitors with relevant details as they move through your venue. When a visitor scans a QR code to open the digital map, nearby beacons share store offers, events, and updates. This setup helps visitors find the shortest routes while keeping them informed about sales and activities, making their journey more engaging and informative.",
    image: "/images/choose-the-right-location.png",
  },
  {
    title: "2. Digitize Your Space",
    content:
      "We create detailed, interactive digital maps of your indoor space, covering entrances, rooms, service zones, and more. Continuously updated, these maps ensure visitors always have the latest information, from new sections to temporary closures, helping them confidently navigate your venue with up-to-date guidance.",
    image: "/images/digitize-your-space.png",
  },
  {
    title: "3. Smart Indoor Navigation, Anytime, Anywhere",
    content:
      "With beComap’s Web App, visitors can access maps from any device, ensuring consistent access throughout the venue. Easily managed through a dashboard, updates to layouts, routes, and restricted areas are quick to make. The Web App provides clear directions, showing the most direct route to help visitors reach their destination efficiently.",
    image: "/images/smart-indoor-navigation-anytime-anywhere.png",
  },
];

const WHY_DATA = [
  {
    title: "Access the Web App",
    description:
      "Visitors can scan a QR code at kiosks or open a web link to access the map without needing to download anything.",
    iconUrl: "/images/icons/website.png",
    activeIconUrl: "/images/icons/website-active.png",
    color: "#34A853",
  },
  {
    title: "Search for a Location",
    description:
      "Users can search for specific locations like stores, rooms, or service areas directly from the map.",
    iconUrl: "/images/icons/find-location.png",
    activeIconUrl: "/images/icons/find-location-active.png",
    color: "#34A853",
  },
  {
    title: "Explore the Map",
    description:
      "Users can pan, zoom, and explore the entire venue to understand the layout and available services.",
    iconUrl: "/images/icons/map-loc.png",
    activeIconUrl: "/images/icons/map-loc-active.png",
    color: "#34A853",
  },
  {
    title: "Navigate to Destination",
    description:
      "Visitors receive clear, step-by-step directions to reach their desired location without confusion.",
    iconUrl: "/images/icons/navigate-map.png",
    activeIconUrl: "/images/icons/navigate-map-active.png",
    color: "#34A853",
  },
];

const WebApp = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <SolutionBanner
        source={"web-app"}
        bgImage={HeroImg}
        title="Web App Solutions with Advanced Indoor Maps for Seamless Navigation"
        description="Let visitors access indoor maps directly from their browser. With beComap’s Web App, guests can find their way through malls, airports, hospitals, or event venues without downloading any apps."
      />
      <IndustryClients />
      <SolutionWhyUs
        title={"What Is the Web App?"}
        description={
          "beComap’s Web App lets users explore and navigate your venue from any device through their browser. It can be accessed by scanning a QR code at kiosks or directly from a web link, eliminating the need for app downloads. The Web App works across mobile, tablet, and desktop devices without compatibility issues."
        }
        bgImage={SolutionWhyUsBg}
        subImage={WhySubImage}
        whyData={WHY_DATA}
      />
      <IndustrySolutions
        image={SecImg}
        solutions={SOLUTIONS}
        title={"What Makes the Web App Effective?"}
        description={{
          part1:
            "The Web App offers up-to-date maps, giving visitors immediate access to the latest information about your venue. Whether there are new stores, layout changes, or temporary closures, the Web App reflects these updates right away. Unlike the mobile app, which requires installation and regular updates, the Web App is accessible instantly through a browser.",
          part2:
            " This makes it more convenient for visitors who don’t want to download an app or deal with compatibility issues.",
          part3:
            "The Web App’s interface is simple to navigate, allowing visitors to easily search for specific locations, view the map, and follow step-by-step directions. While both the mobile app and Web App offer similar functionality, the Web App is particularly beneficial for short-term visitors or those who prefer not to install additional software on their devices. By scanning a QR code or clicking a web link, they can get the information they need quickly, without the need for downloads or updates.",
        }}
      />

      {/* 
      <IndustrySecondSection
        image={SecImg4}
        routeType="shoppingmall"
        title={"No More Mazes. Get Shoppers Straight to the stores."}
        description="Lost customers and missed sales opportunities are things of the past.  beComap's Retail Wayfinding Solution is here to change that. Our system turns confusion into clarity, allowing your mall visitors to navigate your store with ease. They can quickly find what they’re looking for, explore the latest deals, and stay informed about any changes—all without frustration. With beComap, your customers enjoy a continuous shopping experience, and you benefit from happier clients and increased foot traffic and sales."
      />
      <IndustrySolutions
        imageProperty={{
          width: 441,
          height: 505,
          top: "-120px",
          bottom: "0",
          justifyContentParent: "end",
          justifyContent: "end",
          backgroundColor: "gradient",
        }}
        image={SecImg}
        solutions={SOLUTIONS}
        title={"Mall Experience like never before"}
        description="Lost customers and missed sales opportunities are things of the past.  beComap's Retail Wayfinding Solution is here to change that. Our system turns confusion into clarity, allowing your mall visitors to navigate your store with ease. They can quickly find what they’re looking for, explore the latest deals, and stay informed about any changes—all without frustration. With beComap, your customers enjoy a continuous shopping experience, and you benefit from happier clients and increased foot traffic and sales."
      />
      <IndustryExperience
        image={SecImg2}
        title="Mall Navigation Made Easy"
        description="Our solution makes navigating your mall simple and stress-free. It offers detailed info on essential services like restrooms, ATMs, and customer service desks through the app. Shoppers can use easy turn-by-turn directions to find stores and services quickly. By enhancing their overall experience and reducing frustration, more visitors will be encouraged to spend time in our mall, increasing foot traffic and driving sales."
      />
      <IndustryAccordion
        title={"Discover 3X More Stores!"}
        bgImage={BgImg}
        description={
          "With beComap, shoppers get a complete list of all the stores in your mall, including detailed information and current offers. Now, they can explore more shops than ever before and find the best deals, turning every visit into an exciting shopping adventure and driving more foot traffic to your mall."
        }
        accordionData={accordionData}
        videoSrc={"/videos/mall.mp4"}
      />
    
      <IndustryHelp
        title="Why World’s largest venues trust beComap"
        description="beComap changes the way you think about retail navigation & engagement. Our platform simplifies navigation, attracts and retains customers, and optimizes operations to improve the overall shopping journey."
        image={SecImg3}
        points={[
          "Best-in-class indoor mapping for malls",
          "8 years on active R&D in location intelligence",
          "Optimized for use on mobile devices, web browsers, and kiosks",
          "Easy integration with existing apps",
          "Web-based navigation without any downloads",
          "Multiple language support",
        ]}
      />
 */}

      <SolutionsServices
        title="How We Set Up Your Web App for Indoor Navigation"
        description="Setting up your Web App for indoor navigation follows a few important steps to make sure visitors have a clear and easy experience. From choosing the right signal type to creating detailed maps and adding the Web App, we manage everything. The Web App works across all devices without the need for downloads, simplifying navigation for everyone and making your space easier to explore."
        image={"/images/kiosk-dummy.png"}
        serviceData={ServiceData}
      />
      <section
        className="section primary-bg"
        style={{
          backgroundImage: `url("/images/solution-bg.png")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          minHeight: "680px",
        }}
      >
        <Container>
          <Row className="align-items-center">
            <Col lg={7}>
              <Image
                src={"/images/solution-sec.png"}
                alt="indoor mapping software"
                width={550}
                height={450}
                style={{ width: "100%", height: "auto" }}
              />
            </Col>
            <Col lg={5}>
              <div className="section-head">
                <span
                  className="mb-4"
                  style={{
                    fontWeight: 600,
                    fontSize: "18px",
                  }}
                >
                  Case Study
                </span>
                <h2 className="main-title mb-3">Indoor mapping software</h2>
                <p>
                  Develop an ultra-modern travel environment that ree
                  experiences. Set new standards in travel convenience and
                  satisfaction through innovation, sustainability, and
                  operational efficiency.
                </p>
              </div>
              <button className="btn btn-dark mt-4">Learn More</button>
            </Col>
          </Row>
        </Container>
      </section>
      {/* <IndustryEBook
        image={"/images/mall-ebook-bg.png"}
        title={"Retail In Action"}
        description={`<p>According to ACI, global passenger traffic will reach 9.7 billion this year.</p> <p>Learn how to deliver a smooth travel experience with indoor maps in our helpful retail guide.</p>`}
      /> */}
      <Blogs />
      <TalkToOurTeam source={"web-app"} />
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={mallNavigationFAQ}
      />
    </div>
  );
};

export default WebApp;
