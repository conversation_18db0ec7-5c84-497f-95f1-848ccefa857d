'use client'
import { FloatingWhatsApp } from 'react-floating-whatsapp'
import { sendGTMEvent } from '@next/third-parties/google'
import { useEffect, useState } from 'react';
const WhatsAppIcon = () => {
    const [isPageLoaded, setIsPageLoaded] = useState(false);
    useEffect(() => {
        setIsPageLoaded(true);
        return () => {
        };
    }, []);
    return (
        <>{isPageLoaded && <FloatingWhatsApp
            phoneNumber={"+************"}
            accountName={"beComap"}
            avatar={"/beco-icon.jpg"}
            chatMessage={`Hello there! 🤝 \nWelcome to beComaps! \nHow can we help?`}
            // statusMessage=""
            notification
            notificationSound
            onSubmit={(e) => sendGTMEvent({ event: 'WhatsAppButtonClicked', value: 'clicked' })}
        />}</>
    );
}

export default WhatsAppIcon;