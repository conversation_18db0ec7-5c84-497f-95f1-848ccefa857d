.position-row {
  background: #ffffff;
  box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.08);
  border-radius: 20px;
  padding: 35px 30px;
  .nav-item-custom {
    border-radius: 100px;
    height: 64px;
    background-color: rgba(243, 243, 243, 1);
    margin-bottom: 15px;
    margin-top: 15px;
  }

  .nav-link-custom {
    display: flex;
    align-items: center;
    padding-left: 36px;
    height: 100%;
    border-radius: 100px;
    background-color: rgba(243, 243, 243, 1);
    text-align: center;
    font-size: 16px;
    font-family: $font-medium;
    color: black;
  }

  .nav-link.active {
    background-color: $primary-color !important;
    color: white !important;
    border-radius: 100px !important;
  }
  .nav-item {
    border-radius: 100px;
    overflow: hidden;
  }
  .tab-content {
    border: 1px solid rgba(192, 192, 192, 1);
    padding: 32px 43px;
    border-radius: 10px;
  }
}
