import React from 'react';
import { Row, Col, Container } from 'react-bootstrap';
import Image from 'next/image';

const people = [
    {
        name: '<PERSON><PERSON><PERSON>',
        role: 'Co-Founder & CEO',
        image: '/images/vaishak-beco.png'
    },
    {
        name: '<PERSON><PERSON><PERSON>',
        role: 'Co-Founder & COO',
        image: '/images/nidhin-beco.png'
    },
    {
        name: '<PERSON><PERSON><PERSON>',
        role: 'Co-Founder & CTO',
        image: '/images/renjith-beco.png'
    },
    {
        name: '<PERSON><PERSON>',
        role: 'Head of Engineering',
        image: '/images/mithin-beco.png'
    },
    {
        name: '<PERSON><PERSON><PERSON>',
        role: 'Head of Business Development',
        image: '/images/ribin-beco.png'
    },
    {
        name: '<PERSON><PERSON><PERSON>',
        role: 'Head of UI/UX',
        image: '/images/ajmal-beco.png'
    }
];


const PeopleBehind = () => {
    return (
        <section className='section company-peopleBehind-wrap'>
            <Container>
                <h2 className="main-title mb-5 text-center">
                The Amazing People Behind beCo
                </h2>
                <Row className='justify-content-center align-items-center'>
                    {people.map((person, index) => (
                        <Col lg={4} md={6} key={index} className='d-flex justify-content-center align-items-center'>
                            <div className='amz-people-box '>
                                <div className='amz-people-img-wrap'>
                                    <figure>
                                        <Image src={person.image} alt='amazing people' width="223" height="223" />
                                    </figure>
                                </div>
                                <div className='amz-people-names'>
                                    <h4 className='size-20 bold'>{person.name}</h4>
                                    <p className='m-0' style={{ textWrap: 'nowrap' }}>{person.role}</p>
                                </div>

                            </div>
                        </Col>
                    ))}
                </Row>
            </Container>
        </section>
    );
};

export default PeopleBehind;
