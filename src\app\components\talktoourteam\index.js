"use client";
import { Container, Row, Col, Form, Button } from "react-bootstrap";
import Image from "next/image";
import { formSubmit } from "@/app/action/action";
import { Formik } from "formik";
import { useRouter } from "next/navigation";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/bootstrap.css";
import { useRef, useState } from "react";
import { startConfetti } from "@/app/utils/contactBtnAnimation";

const TalkToOurTeam = () => {
  const router = useRouter();
  const [phone, setPhone] = useState("");

  const [formData, setFormData] = useState({
    name: "",
    phone_number: "",
    option: "",
  });

  const [formStatus, setFormStatus] = useState(false);
  const buttonRef = useRef(null);
  const textRef = useRef(null);
  return (
    <div className="section talktoteam-section">
      <Container>
        <Row className="talktoteam-wrap">
          <Col xl={6}  className="talktoteam-left d-none d-xl-block">
            <Image
              src="/images/talktoteam-img.png"
              alt="talk with our team"
              width={570}
              height={452}
            />
          </Col>
          <Col xl={6} lg={12} className="client-feedback-right flex-1">
            <div className="section-head">
              <h2 className="main-title mb-4">
                Ready to unlock the power of location?
              </h2>
              <p>
                Discover the power of indoor positioning and tracking - contact
                us today!{" "}
              </p>
            </div>
            <div className="talkto-form-wrap">
              <Formik
                initialValues={{
                  name: "",
                  phone_number: "",
                  option: "",
                }}
                validate={(values) => {
                  const errors = {};
                  if (!values.name) {
                    errors.name = "Name is required";
                  }

                  if (!values.option) {
                    errors.option = "Please select an option";
                  }

                  return errors;
                }}
                onSubmit={async (values, { setSubmitting, resetForm }) => {
                  try {
                    // Perform your form submission here
                    const status = await formSubmit(values, "contact-sales");

                    if (status === 201) {
                      // Reset the form and set a success message
                      setSubmitting(false);
                      setFormStatus(true);
                      startConfetti(buttonRef, textRef);

                      // // router.push("/thankyou");
                      // setTimeout(() => {
                      //   setFormStatus(false);
                      //   setSubmitting(false);
                      //   resetForm();
                      //   setFormData({
                      //     name: "",
                      //     email: "",
                      //     phone_number: "",
                      //     message: "",
                      //     option: "",
                      //   });
                      // }, 5000);
                    } else {
                      alert(`Form submission failed with status: ${status}`);
                    }
                  } catch (error) {
                    alert(`Form submission failed: ${error.message}`);
                  }
                }}
              >
                {({
                  values,
                  errors,
                  touched,
                  handleChange,
                  handleBlur,
                  handleSubmit,
                  isSubmitting,
                  setFieldValue,
                  dirty,
                  isValid,
                }) => (
                  <Form onSubmit={handleSubmit}>
                    <Row className="talkto-form-row">
                      <Col md={6}>
                        <Form.Group className="talkto-form-item  position-relative">
                          <Form.Control
                            className="form-control"
                            type="text"
                            name="name"
                            placeholder="Name"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.name}
                            isInvalid={errors.name && touched.name}
                          />
                          <Form.Control.Feedback tooltip>
                            {errors.name && touched.name && errors.name}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="talkto-form-item  position-relative">
                          <PhoneInput
                            inputProps={{
                              name: "phone_number",
                              onBlur: handleBlur,
                            }}
                            placeholder="Phone Number*"
                            country={"us"}
                            enableSearch={true}
                            onChange={(phone) =>
                              setFieldValue("phone_number", phone)
                            }
                            value={phone}
                            inputStyle={{
                              paddingLeft: "60px!important",
                            }}
                            inputClass={`w-100 ${
                              touched.phone_number && errors.phone_number
                                ? "is-invalid"
                                : ""
                            }`}
                          />
                          {errors.phone_number && touched.phone_number && (
                            <div className="invalid-feedback d-block">
                              {errors.phone_number}
                            </div>
                          )}
                        </Form.Group>
                      </Col>
                      <Col md={12}>
                        <Form.Group className="talkto-form-item position-relative">
                          <Form.Select
                            name="option"
                            value={values.option}
                            onChange={(e) =>
                              setFieldValue("option", e.target.value)
                            }
                            onBlur={handleBlur}
                            isInvalid={touched.option && !!errors.option}
                          >
                            <option value="">Choose...</option>
                            <option value="Indoor navigation">
                              Indoor navigation
                            </option>
                            <option value="Asset tracking">
                              Asset tracking
                            </option>
                            <option value="General Inquiry">
                              General Inquiry
                            </option>
                          </Form.Select>
                          <Form.Control.Feedback tooltip>
                            {errors.option}
                          </Form.Control.Feedback>
                          <span className="select-arrow">
                            <svg
                              width="14"
                              height="9"
                              viewBox="0 0 14 9"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M14 1.35669L12.7575 0L7 6.28662L1.2425 0L0 1.35669L7 9L14 1.35669Z"
                                fill="black"
                              />
                            </svg>
                          </span>
                        </Form.Group>
                      </Col>
                      <Col md={12}>
                        <Button
                          ref={buttonRef}
                          id="button-conf"
                          variant="secondary"
                          type="submit"
                          disabled={isSubmitting}
                        >
                          <span className="custom-loader" />
                          <span ref={textRef} id="text">
                            Talk to our Team
                          </span>
                        </Button>
                      </Col>
                    </Row>
                  </Form>
                )}
              </Formik>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default TalkToOurTeam;
