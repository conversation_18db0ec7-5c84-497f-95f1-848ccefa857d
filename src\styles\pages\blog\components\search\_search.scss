.blog-search {
  padding: 17px;
  border-radius: 20px;
  background: rgba(240, 240, 240, 1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 392px;
}
.blog-search-row {
  padding-bottom: 15px;
  padding-top: 15px;
  h4 {
    font-family: $font-bold;
    font-size: 18px;
    line-height: 40px;
  }
  .form-control {
    height: 60px;
    border-radius: 100px;
    border: 1px solid rgba(217, 204, 204, 1);
    padding: 0 50px 0 15px;
    position: relative;
    z-index: 2;
  }
  button.select-arrow {
    background: transparent;
    outline: none;
    border: none;
    pointer-events: inherit;
    z-index: 3;
  }
  .blog-search-suggestions-wrap {
    position: absolute;
    width: 100%;
    z-index: 1;
    background: #fff;
    top: 100%;
    border-radius: 10px;
    border: 1px solid rgba(230, 230, 230, 0.53);
    box-shadow: 0px 10px 50px rgba(0, 0, 0, 0.15);
    max-height: 380px;
    border-radius: 20px;
    margin-top: 10px;
    overflow: hidden;
    .no-results {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

  }
  .blog-search-suggestions {
    max-height: 380px;
    padding: 0;

    list-style: none;
    margin: 0;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 5px;
      margin-right: 10px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #000;
    }
    &::-webkit-scrollbar-thumb:hover {
      background-color: #000;
    }

    li {
      //   margin-bottom: 15px;
      list-style: none;

      .suggestion-link {
        color: #000;
        font-size: 14px;
        font-family: $font-medium;
        text-decoration: none;
        padding: 15px;
        line-height: 23px;
        line-height: 21px;
        display: block;
        border-bottom: 1px solid #dcdcdc;

        &:hover {
          background-color: #f0f0f0;
        }
      }
    }
  }
}
.blog-form-item {
  position: relative;
  .form-select {
    border: 1px solid rgba(230, 230, 230, 0.53);
    border-radius: 100px;
    height: 60px;
    background-color: #fff;
    font-size: 14px;
    font-family: $font-medium;
    background-image: none;
    padding: 0 20px;
    cursor: pointer;
  }
}

.skeleton-loader {
  list-style: none;
  padding: 0;
  margin: 0;
}

.skeleton-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 10px 0;
}

.skeleton-img {
  width: 72px;
  height: 72px;
  background-color: #e0e0e0;
  border-radius: 8px;
}

.skeleton-text {
  width: 70%;
  height: 16px;
  background-color: #e0e0e0;
  border-radius: 4px;
}