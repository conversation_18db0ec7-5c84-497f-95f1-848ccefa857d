import React from "react";
import Image from "next/image";
import { Container, Row, Col } from "react-bootstrap";

const MappingPartner = () => {
  return (
    // <div className="mapping-partner-row d-flex p-relative">
    //     <div className="mapping-partner-left">&nbsp;</div>
    //     <div className="mapping-vector-bg">&nbsp;</div>
    //     <div className="mapping-partner-right flex-1 ">

    // </div>
    <section className="section mapping-partner-section">
      <Container fluid={"lg"}>
        <Row className="align-items-center justify-content-center gx-xl-5">
          <Col xxl={6} lg={6} className="order-2 order-lg-1">
            <div className="section-head">
                <Image src="/images/mapping-sec-leftbg.png" alt="mapping-partner" width={0} height={0} style={{width: '100%', height: 'auto'}} className="mapping-partner-img" />
            </div>
          </Col>
          <Col xxl={5} lg={6} className="order-1 order-lg-2">
            <h2 className="main-title mb-4">
              Mapping Partner for the Smart Building Space
            </h2>
            <p className="main-desc">
              At Becomap, we provide powerful indoor navigation software and
              related location services that help businesses to enhance their
              customer experiences and efficient operations within vast and
              complex indoor environments. We use the latest innovative indoor
              navigation technologies that work to establish seamless indoor
              way-finding to minimize confusion and maximize operational
              efficiency.
            </p>
            <p className="main-desc">
              Our indoor navigation system is perfectly suited to be employed in
              large shopping malls, big hospitals, busy convention centers,
              airports, or even vast commercial office complexes. Designed to
              offer real-time, accurate way-finding solutions, our applications
              are designed to improve visibility while simultaneously improving
              the different levels of control over your resources.
            </p>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default MappingPartner;
