import React from 'react';
import CaseStudyItem from './_components/casestudyitem';
import CaseStudyBanner from './_components/casestudybanner';
import TalkToOurTeam from '@/app/components/talktoourteam';


const caseStudyData = [
    { image: '/images/casestudy-img-2.png', title: 'Oasis Living', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { image: '/images/casestudy-img-1.png', title: 'Another Project', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { image: '/images/casestudy-img-2.png', title: 'Oasis Living', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { image: '/images/casestudy-img-1.png', title: 'Another Project', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { image: '/images/casestudy-img-2.png', title: 'Oasis Living', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { image: '/images/casestudy-img-1.png', title: 'Another Project', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { image: '/images/casestudy-img-2.png', title: 'Oasis Living', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { image: '/images/casestudy-img-1.png', title: 'Another Project', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    { image: '/images/casestudy-img-2.png', title: 'Oasis Living', description: 'Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamless experiences.', },
    // Add more sections as needed
];


const CaseStudy = () => {
    return (
        <div className='casestudy-wrap'>
            <CaseStudyBanner />
            {caseStudyData.map((section, index) => (
                <CaseStudyItem
                    key={index}
                    index={index}
                    image={section.image}
                    content={section.content}
                    title={section.title}
                    description={section.description}
                />
            ))}
            <TalkToOurTeam />
        </div>
    );
};

export default CaseStudy;
