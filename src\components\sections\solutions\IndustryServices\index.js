"use client";
import Image from "next/image";
import { Container, Row, Col } from "react-bootstrap";

const ServiceContent = ({ data, isReversed }) => (
  <div
    className={`service-item-content  ${
      isReversed ? "reverse-content text-start" : " text-start"
    }`}
  >
    <div
      className={`service-icon-wrap d-flex flex-column ${
        isReversed
          ? "align-items-lg-start align-items-center"
          : "align-items-lg-start align-items-center"
      }`}
    >
      {data.icon && (
        <span className="srv-icon">
          <Image
            src={data.icon}
            alt={data.title}
            width={30}
            height={30}
            className="solution-icon-img"
          />
        </span>
      )}
      <h4 className="small-head">{data.title}</h4>
    </div>
    <p className="mb-0">{data.content}</p>
  </div>
);

const SolutionsServices = ({
  title,
  description,
  serviceData,
  image,
  isIndoor,
}) => {
  return (
    <section className={`section p-0 service-section solutn overflow-hidden`}>
      <Container>
        <Row
          className={`align-items-center pt-5 pb-4 ${
            isIndoor ? "mt-5 pb-5 mb-5" : "gx-lg-5"
          }`}
        >
          {isIndoor ? (
            <>
              <Col lg={6} className="order-2 order-lg-1 mt-lg-0 mt-5">
                <div className="service-timeline">
                  <ul>
                    <li>
                      <div className="icon-sec">
                        <Image
                          src="/images/icons/map_2.png"
                          alt="banner-image"
                          width={35}
                          height={35}
                        />
                      </div>
                      <div className="content-sec">
                        <h3>Create a Digital Map</h3>
                        <p>
                          We design a detailed map based on the venue’s floor
                          plan, covering all key areas like stores, services,
                          and facilities.
                        </p>
                      </div>
                    </li>
                    <li>
                      <div className="icon-sec">
                        <Image
                          src="/images/icons/display.png"
                          alt="banner-image"
                          width={35}
                          height={35}
                        />
                      </div>
                      <div className="content-sec">
                        <h3>Set Up Access Points</h3>
                        <p>
                          Provide access through a web app, kiosk, or integrate
                          it with an existing app for visitor convenience.
                        </p>
                      </div>
                    </li>
                    <li>
                      <div className="icon-sec">
                        <Image
                          src="/images/icons/qr-code-1.png"
                          alt="banner-image"
                          width={35}
                          height={35}
                        />
                      </div>
                      <div className="content-sec">
                        <h3>Scan to Begin</h3>
                        <p>
                          Visitors scan a QR code to open the map and start
                          their journey through the venue.
                        </p>
                      </div>
                    </li>
                    <li>
                      <div className="icon-sec">
                        <Image
                          src="/images/icons/image-route.png"
                          alt="banner-image"
                          width={35}
                          height={35}
                        />
                      </div>
                      <div className="content-sec">
                        <h3>Follow the Fastest Route</h3>
                        <p>
                          The system shows a direct route to the selected
                          destination, helping visitors reach their goal without
                          unnecessary steps.
                        </p>
                      </div>
                    </li>
                  </ul>
                </div>
              </Col>
              <Col lg={6} className="order-1 order-lg-2">
                <div className="soultion-details px-0">
                  <h2 className="main-title mb-3">
                    How Does Indoor Mapping Work?
                  </h2>
                  <p
                  // style={{
                  //   paddingRight: "9%",
                  // }}
                  >
                    Our indoor mapping solution helps visitors find their way by
                    guiding them along the fastest route to their destination.
                    With clear digital maps and convenient access points,
                    navigating large spaces becomes straightforward. With highly
                    detailed digital maps, users can easily visualize their
                    journey and stay on track without getting lost. By scanning
                    QR codes or using direct URL links placed strategically
                    throughout the building, visitors can instantly access
                    interactive maps on their devices, enabling smooth
                    navigation right from their current location.
                  </p>
                  <p className="mb-0">
                    The same solution supports multi-level navigation, making it
                    ideal for buildings with multiple floors since users will be
                    given clear instructions on how to make easy transitions.
                    Businesses can edit the maps to feature various points of
                    interest, allow branding, and tailor different features to
                    their needs. Moreover, analytics derived from the
                    interaction of users help indicate high-traffic areas to
                    optimize space usage and also improve visitor experiences
                    through custom enhancements.
                  </p>
                </div>
              </Col>
            </>
          ) : (
            <>
              <Col xl={7} lg={6} className="order-2 order-lg-1">
                <div className="im-wrapper no-befre ms-xl-5 ms-0">
                  <Image
                    src={image}
                    alt="banner-image"
                    width="452"
                    height="368"
                    style={{ width: "100%", height: "auto" }}
                  />
                </div>
              </Col>
              <Col xl={5} lg={6} className="order-1 order-lg-2">
                <div className="soultion-details mb-lg-0 mb-4">
                  <h2 className="main-title mb-3">{title}</h2>
                  <p className="mb-0">{description}</p>
                </div>
              </Col>
            </>
          )}
        </Row>
      </Container>
      <Container fluid style={{ padding: "0px" }}>
        {/* <div className="company-value-head">
          <h2 className="main-title">{title}</h2>
          <p>{description}</p>
        </div> */}
        {serviceData.map((service, index) => {
          const isReversed = index % 2 !== 0;
          return (
            <Row
              key={index}
              className={`align-items-center g-0 position-relative ser-cr ${
                isReversed ? "justify-content-start" : "justify-content-end"
              }`}
              style={{ backgroundColor: isReversed ? "#ffffff" : "#f3f3f3" }}
            >
              {isReversed ? (
                <>
                  <Col lg={6}>
                    <div className="im-wrapper reverse-img">
                      <Image
                        src={service.image}
                        alt={service.title}
                        width="452"
                        height="368"
                        style={{ width: "100%", height: "auto" }}
                      />
                    </div>
                  </Col>
                  <Col xxl={5} lg={6}>
                    <ServiceContent data={service} isReversed={isReversed} />
                  </Col>
                </>
              ) : (
                <>
                  <Col xxl={5} lg={6} className="order-2 order-lg-1">
                    <ServiceContent data={service} isReversed={isReversed} />
                  </Col>
                  <Col lg={6} className="order-1 order-lg-2">
                    <div className="im-wrapper">
                      <Image
                        src={service.image}
                        alt={service.title}
                        width="452"
                        height="368"
                        style={{ width: "100%", height: "auto" }}
                      />
                    </div>
                  </Col>
                </>
              )}
            </Row>
          );
        })}
      </Container>
    </section>
  );
};

export default SolutionsServices;
