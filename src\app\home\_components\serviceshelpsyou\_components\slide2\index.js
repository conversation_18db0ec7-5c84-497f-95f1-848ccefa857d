import React from "react";
import Link from "next/link";
import Image from "next/image";


const ServicesHelpsSlider2 = () => {
    return (
        <div className="srv-helpslide-wrap">
            <div className="srv-helpslide-box d-flex">
                <div className="srv-helpslide-box-left">
                    <div className="srv-count-box">
                        <h2>9,00+</h2>
                        <div className="srv-count-title">Maps</div>
                    </div>
                    <div className="srv-count-box">
                        <h2>1.5+</h2>
                        <div className="srv-count-title">Billion sqft</div>
                    </div>
                </div>
                <div className="srv-helpslide-box-mid d-flex">
                    <div className="srv-helpslide-imgbox srv-helpslide-imgbox1">
                        <h4>Retail</h4>
                        <Image
                            src="/images/helpslide1-img-1.png"
                            alt="Service help"
                            width={0}
                            height={0}
                            fill="true"
                        />
                    </div>
                    <div className="srv-helpslide-imgbox srv-helpslide-imgbox1">
                        <Image
                            src="/images/helpslide1-img-2.png"
                            alt="Service help"
                            width={0}
                            height={0}
                            fill="true"
                        />
                        <h4>Shopping Malls</h4>
                    </div>
                    <div className="srv-helpslide-imgbox srv-helpslide-imgbox1">
                        <h4>Airports</h4>
                        <Image
                            src="/images/helpslide1-img-3.png"
                            alt="Service help"
                            width={0}
                            height={0}
                            fill="true"
                        />
                    </div>
                    <div className="srv-helpslide-imgbox srv-helpslide-imgbox1">
                        <h4>Stadiums</h4>
                        <Image
                            src="/images/helpslide1-img-4.png"
                            alt="Service help"
                            width={0}
                            height={0}
                            fill="true"
                        />
                    </div>
                </div>
                <div className="srv-helpslide-box-right">
                    <div className="srv-slide-content">
                        At Becomap, we provide powerful indoor navigation software andexperiences and
                        streamline operainding to minimize confusion.
                    </div>
                </div>
            </div>
        </div>

    );
};

export default ServicesHelpsSlider2;
