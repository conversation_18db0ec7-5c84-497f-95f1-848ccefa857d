"use client";
import Image from "next/image";
import useS<PERSON> from "swr";
import axios from "axios";
import Loading from "@/app/components/loader/loader";
import { Container, Row } from "react-bootstrap";
import BlogDetailContent from "../blogdetailcontent";
import BlogDetailsLeft from "../blogdetailsleft";
import { useEffect, useState } from "react";
import { slugify } from "@/app/utils/slugify";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const fetcher = (url) =>
  axios
    .get(url, {
      headers: {
        "X-Blog-Identifier": "beco",
      },
    })
    .then((res) => res.data);

export default function BlogDetails({ slug }) {
  const { data, error } = useSWR(
    [`https://blog-api.becomap.com/posts/${slug}`],
    fetcher,
    { refreshInterval: 1000 }
  );

  const [updatedContent, setUpdatedContent] = useState("");
  const [toc, setToc] = useState([]);

  useEffect(() => {
    if (!data?.body) return; // If there's no content, stop execution

    // Parse the content to find headings (h1, h2, h3)
    const parser = new DOMParser();
    const doc = parser.parseFromString(data.body, "text/html");
    const headings = doc.querySelectorAll("h1, h2, h3");

    if (!headings || headings.length === 0) {
      console.warn("No headings found in the content");
      return;
    }

    const tocList = [];
    headings.forEach((heading) => {
      const text = heading.textContent;
      const id = slugify(text); // Create a unique id based on the heading text

      heading.setAttribute("id", id); // Add id to the heading element
      tocList.push({
        id,
        text,
        level: heading.tagName.toLowerCase(), // h1, h2, h3
      });
    });

    setToc(createNestedToc(tocList));

    // Serialize the updated HTML with the new ids
    setUpdatedContent(doc.body.innerHTML);
  }, [data?.body]);

  const createNestedToc = (tocList) => {
    const nestedToc = [];
    let currentMain = null; // Holds the latest h1 or h2
    let currentH2 = null; // Holds the latest h2

    tocList.forEach((item) => {
      if (item.level === "h1") {
        // If it's an h1, it becomes the new main heading
        currentMain = { ...item, children: [] };
        nestedToc.push(currentMain);
        currentH2 = null; // Reset h2 for new h1
      } else if (item.level === "h2") {
        if (currentMain) {
          // If h1 exists, h2 is added under the current h1
          currentH2 = { ...item, children: [] };
          currentMain.children.push(currentH2);
        } else {
          // If no h1, h2 becomes a new main heading
          currentH2 = { ...item, children: [] };
          nestedToc.push(currentH2);
        }
      } else if (item.level === "h3" && currentH2) {
        // h3 is nested under the current h2
        currentH2.children.push(item);
      }
    });

    return nestedToc;
  };

  // Move loading and error handling after hooks to ensure all hooks are called
  // if (error) return <Loading />;
  // if (!data) return <Loading />;

  const formateDate = (date) => {
    const originalDate = new Date(date);
    const options = { day: "numeric", month: "short", year: "numeric" };
    return originalDate.toLocaleDateString(undefined, options);
  };

  return (
    <div className="blog-detailpage-wrap">
      <Container className="blog-detail-container">
        <div className="blog-detail-header">
          <div className="d-flex justify-content-between blogpost-topic-info-wrap">
            <div className="blogpost-topic-info d-flex align-items-center">
              <span className="blogpost-topic">{data?.category?.title}</span>
              <span className="blogpost-author">{data?.author?.name}</span>
            </div>
            <div className="blog-date">
              {data && formateDate(data?.publication_date)}
            </div>
          </div>
          <h2 className="blogdetailpost-heading mt-4 mb-3">
            {data ? data?.title : <Skeleton count={2} />}
          </h2>
          <div className="blog-postimage-wrap">
            {data ? (
              <Image
                src={data?.image}
                alt={data?.title}
                width="100"
                height="585"
                style={{ width: "100%", height: "auto" }}
                layout="responsive"
              />
            ) : (
              <Skeleton height={585} style={{ borderRadius: "20px" }} />
            )}
          </div>
        </div>
        <Row className="blog-row flex-lg-row flex-column">
          <div className="blog-left">
            {data ? (
            <BlogDetailsLeft title={data?.title} tableOfContent={toc} />
            )
          : (
            <>
            <Skeleton count={12} />
            </>
          )}
          </div>
          <div className="blog-right flex-1">
            {data ? (
            <BlogDetailContent content={updatedContent} />
              
            ): (
              <>
              <Skeleton count={12.5} />
              <Skeleton count={22.5} />
              </>
            )}
          </div>
        </Row>
      </Container>
    </div>
  );
}
