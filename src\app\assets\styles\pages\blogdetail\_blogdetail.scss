.blogpost-topic-info-wrap {
  border-bottom: 1px solid rgba(217, 217, 217, 1);
}
.blog-date {
  font-family: $font-bold;
  font-size: 15px;
}
.blogdetailpost-heading {
  font-family: $font-bold;
  font-size: 40px;
  line-height: 50px;
}
.blog-detail-container {
  max-width: 1120px;
  @media (max-width: 991px) {
    padding:0 30px;
  }
}
.blog-left {
  width: 35%;
  @media (max-width: 991px) {
    width: 100%;
    .blog-details-left-wrap {
      display: flex;
      flex-direction: column-reverse;
    }
  }
}
.blog-right {
  width: 65%;
  @media (max-width: 991px) {
    width: 100%;
  }
}
