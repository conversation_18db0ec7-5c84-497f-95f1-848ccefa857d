/** @type {import('next').NextConfig} */
const nextConfig = {
    webpack(config) {
        // Adding rule for PDF files
        config.module.rules.push({
            test: /\.pdf$/,
            use: ["raw-loader"]
        });

        // Adding rule for video files (e.g., .mp4)
        config.module.rules.push({
            test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)$/,
            use: {
                loader: 'file-loader',
                options: {
                    name: '[path][name].[ext]',
                },
            },
        });

        return config;
    },
    output: 'export',
    distDir: 'build',
    trailingSlash: true,
    images: {
        dangerouslyAllowSVG: true,
        unoptimized: true,
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'd3tit0gyhwdxsi.cloudfront.net',
                pathname: '**',
            },
        ],
    },
}

export default nextConfig;
