.company-banner-image {
  background-image: url("/images/company-banner-bg.png");
  background-color: $primary-color;
  border-radius: 30px;
}
.company-banner-wrap {
  background: rgba(243, 243, 243, 1);
  padding: 40px 0;
  .main-title {
    @media (max-width: 1199px) {
      font-size: 35px;
      line-height: 40px;
    }
    @media (max-width: 991px) {
      font-size: 30px;
      line-height: 35px;
    }
  }
}
.company-banner-details-right {
  padding-left: 30px;
  @media (max-width: 1057px) {
    padding-left: 10px;
  }
  .btn {
    min-width: 344px;
  }
  @media (max-width:344px) {
    .btn {
        min-width: 100%;
    }
  }
}
.company-banner-details {
  p {
    margin-bottom: 30px;
    @media (max-width: 991px) {
        margin-bottom: 25px;
      line-height: 26px;

    }
 
  }
}
.company-banner-image {
  padding-top: 150px;
}
.company-banneslider {
//   @media (max-width: 767px) {
//     position: absolute;
//     bottom: 0;
//     left: 0;
//     right: 0;
//   }
  h4 {
    font-size: 22px;
    font-family: $font-bold;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    white-space: nowrap;
 
    @media (max-width: 767px) {
      margin-bottom: 25px;
      margin-top: 20px;
      margin-left: 10px;
    }
    span {
      font-family: $font;
      font-size: 18px;
      position: relative;
      line-height: 28px;
      padding-left: 10px;
      margin-left: 3px;
      display: inline-block;
      white-space: wrap;

      &::after {
        content: "";
        position: absolute;
        left: 3px;
        top: -2px;
        bottom: 4px;
        width: 2px;
        background-color: #000;
      }
    }
  }
}
@media (max-width: 1400px) {
  .company-banneslider {
    h4 {
      font-size: 18px;
      span {
        font-size: 15px;
        position: relative;
        line-height: 22px;
      }
    }
  }
}

@media (max-width: 767px) {
//   .cmp-row {
//     padding-bottom: 150px;
//   }
}
