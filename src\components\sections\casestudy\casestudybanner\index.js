import React from 'react';
import { Row, Col, Container } from 'react-bootstrap';
import Button from 'react-bootstrap/Button';
import Image from 'next/image';

const CaseStudyBanner = () => {
    return (
        <section className='casestudy-banner-wrap'>
            <div className='casestudy-bannner-bg'>
                <Image src='/images/casestudy-banner-img.png' alt='casestudy banner' width="1000" height="400" style={{ width: '100%', height: 'auto' }} />
            </div>
            <div className='casestudy-bannner-overlay'>
                <Container>
                    <Row className='justify-content-start align-items-center'>

                        <Col md={6}>
                            <div className='casestudy-banner-details'>
                                <h2 className='main-title mb-24'>Discover Our Projects</h2>
                                <p className='mb-24'>Elevate Passenger Experience and Boost Airport Efficiency, streamline operations, and unlock new revenue opportunities with our user-friendly maps and navigation available on every passenger’s smartphone.
                                </p>
                                <div className='btn-wrap double-btn'>
                                    <Button className="btn-secondary">Let&apos;s talk to Sales</Button>
                                    <Button className="btn-white">See Demo</Button>
                                </div>
                            </div>
                        </Col>
                    </Row>
                </Container>
            </div>

        </section >
    );
};

export default CaseStudyBanner;
