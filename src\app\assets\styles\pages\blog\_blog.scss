.blog-top-row {
  margin: 0 -12px;

  @media (max-width: 991px) {
    flex-direction: column-reverse;
  }
  .blog-top-post-left {
    width: 65%;
    padding: 0 12px;
    @media (max-width: 991px) {
      width: 100%;
    }
    .blog-postimage-wrap {
      img {
        height: 392px !important;
      }
    }
  }
  .blog-top-post-right {
    width: 35%;
    padding: 0 12px;
    @media (max-width: 991px) {
      width: 100%;
      margin-bottom: 20px;
    }
  }
}
.blog-post-smallbox {
  h1.blogpost-heading {
    font-size: 22px;
    line-height: 30px;
  }
}
.blog-page-wrap {
  padding-top: 40px;
}

.category-title {
  background-color: #f5f5f5;
  padding: 30px 20px;
  border-radius: 20px;
  margin-bottom: 25px;
  h2 {
    font-size: 40px;
    text-align: left;
    margin-bottom: 15px;
    @media (max-width: 991px) {
      font-size: 30px;
    }
  }
  .category-title-top-row {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: auto;
    gap: 5px;
    scrollbar-width: none;
    touch-action: pan-y;
    a {
      text-decoration: none;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #000;
      margin: 0;
      background: #fff;
      border: 1px solid #e0e0e0;
      padding: 4px 10px;
      border-radius: 9px;
      font-size: 14px;
      line-height: 20px;
      cursor: pointer;
      white-space: nowrap;
      &:hover {
        background: $primary-color;
        color: #fff;
      }
    }
  }
}
