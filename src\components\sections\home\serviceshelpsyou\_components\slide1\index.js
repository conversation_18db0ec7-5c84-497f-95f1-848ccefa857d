import React from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";

const ServicesHelpsSlider1 = () => {
  const router = useRouter();
  return (
    <div className="srv-helpslide-box-mid d-flex">
      <div className="srv-helpslide-imgbox srv-helpslide-imgbox1 d-flex flex-md-column justify-content-center">
        <h4 className="text-end">Airport</h4>
        <div className="helpyou-overlay-wrap">
          <div className="helpyou-overlay-item">
            <span>
              <button className="btn-secondary btn btn-primary" onClick={() => router.push("/industry/airport-indoor-navigation")} >Read More</button>
            </span>
          </div>
          <Image
            src="/images/airport.png"
            alt="Service help"
            width={0}
            height={0}
            style={{ width: "100%", height: "100%" }}
          />
        </div>
      </div>
      <div className="srv-helpslide-imgbox srv-helpslide-imgbox2 d-flex flex-md-column justify-content-start">
        <div className="helpyou-overlay-wrap">
          <div className="helpyou-overlay-item">
            <span>
              <button className="btn-secondary btn btn-primary" onClick={() => router.push("/industry/shopping-malls-indoor-navigation")}>Read More</button>
            </span>
          </div>
          <Image
            src="/images/shoppingmall.png"
            alt="Service help"
            width={0}
            height={0}
            style={{ width: "100%", height: "100%" }}
          />
        </div>
        <h4>Shopping Malls</h4>
      </div>
      <div className="srv-helpslide-imgbox srv-helpslide-imgbox3 d-flex flex-md-column justify-content-center">
        <h4>Health Care</h4>
        <div className="helpyou-overlay-wrap">
          <div className="helpyou-overlay-item">
            <span>
              <button className="btn-secondary btn btn-primary" onClick={() => router.push("/industry/healthcare-indoor-navigation")}>Read More</button>
            </span>
          </div>
          <Image
            src="/images/healthcare.png"
            alt="Service help"
            width={0}
            height={0}
            style={{ width: "100%", height: "100%" }}
          />
        </div>
      </div>
      <div className="srv-helpslide-imgbox srv-helpslide-imgbox4 d-flex flex-md-column justify-content-end">
        <h4>Stadiums & Theme Parks</h4>
        <div className="helpyou-overlay-wrap">
          <div className="helpyou-overlay-item">
            <span>
              <button className="btn-secondary btn btn-primary" onClick={() => router.push("/industry/stadiums-theme-parks-indoor-navigation")}>Read More</button>
            </span>
          </div>
          <Image
            src="/images/stadiums.png"
            alt="Service help"
            width={0}
            height={0}
            style={{ width: "100%", height: "100%" }}
          />
        </div>
      </div>
    </div>
  );
};

export default ServicesHelpsSlider1;
