.contact-left-col {
  width: 45%;

  @media (max-width: 1015px) {
    width: 100%;
    order: 2;
    align-items: center;
    display: flex;
  }
}
.contact-right-col {
  width: 55%;
  @media (max-width: 1015px) {
    width: 100%;
    order: 1;
  }
  @media (max-width: 1199px) {
    // width: 100%;
    .section-head {
      margin-bottom: 20px;
      .main-title {
        font-size: 34px;
        line-height: 38px;
        margin-bottom: 10px;
      }
      p {
        margin-bottom: 0px;
      }
    }
  }
}
.contact-wrap {
  padding-top: 24px;
}

.contact-form-wrap {
  @media (max-width: 1015px) {
    margin: 0 0 50px;
  }
}

.loader {
  display: none;
  justify-content: center;
  align-items: center;
  position: relative;
  margin: 0 -5%;
  overflow: hidden;
  .dot-elastic {
    position: relative;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: #9880ff;
    color: #9880ff;
    animation: dot-elastic 1s infinite linear;

    &::before,
    &::after {
      content: "";
      display: inline-block;
      position: absolute;
      top: 0;
    }

    &::before {
      left: -15px;
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background-color: #9880ff;
      color: #9880ff;
      animation: dot-elastic-before 1s infinite linear;
    }

    &::after {
      left: 15px;
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background-color: #9880ff;
      color: #9880ff;
      animation: dot-elastic-after 1s infinite linear;
    }
  }

  @keyframes dot-elastic-before {
    0% {
      transform: scale(1, 1);
    }
    25% {
      transform: scale(1, 1.5);
    }
    50% {
      transform: scale(1, 0.67);
    }
    75% {
      transform: scale(1, 1);
    }
    100% {
      transform: scale(1, 1);
    }
  }

  @keyframes dot-elastic {
    0% {
      transform: scale(1, 1);
    }
    25% {
      transform: scale(1, 1);
    }
    50% {
      transform: scale(1, 1.5);
    }
    75% {
      transform: scale(1, 1);
    }
    100% {
      transform: scale(1, 1);
    }
  }
  @keyframes dot-elastic-after {
    0% {
      transform: scale(1, 1);
    }
    25% {
      transform: scale(1, 1);
    }
    50% {
      transform: scale(1, 0.67);
    }
    75% {
      transform: scale(1, 1.5);
    }
    100% {
      transform: scale(1, 1);
    }
  }
}


.form-btn  {
    position: relative;
    &.loading {
        span {
            display: none;
        }
        .loader {
            display: flex;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
    }
}