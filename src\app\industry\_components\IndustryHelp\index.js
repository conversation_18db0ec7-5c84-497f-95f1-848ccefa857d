"use client";
import Image from "next/image";
import { Container, Row, Col } from "react-bootstrap";

const IndustryHelp = ({ title, description, image, points }) => {
  return (
    <section className="section service-section bg-bc-light ind-pn">
      <Container fluid={"lg"}>
        <div className="company-value-head mb-lg-5 mb-3">
          <h2 className="main-title">{title}</h2>
          <p className="mx-3">{description}</p>
        </div>
        <Row className="align-items-center">
          <Col lg={6}>
            <div className="me-4">
              <Image
                src={image.src}
                alt="banner-image"
                width="452"
                height="368"
                style={{ width: "100%", height: "auto" }}
              />
            </div>
          </Col>
          <Col lg={6}>
            <div className="key-points-wrap">
              <ul>
                {points.map((point, index) => (
                  <li key={`point-${index}`}>
                    <span className="list-tick-img rounded-pill secondary-bg ">
                      <svg
                        width="22"
                        height="22"
                        viewBox="0 0 27 27"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10.7445 20.2508L4.33203 13.8383L5.93516 12.2352L10.7445 17.0445L21.0664 6.72266L22.6695 8.32578L10.7445 20.2508Z"
                          fill="#E8EAED"
                        />
                      </svg>
                    </span>
                    {point}
                  </li>
                ))}
              </ul>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default IndustryHelp;
