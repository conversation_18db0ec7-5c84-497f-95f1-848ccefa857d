.why-us-section {
    .why-wrapper {
      background: linear-gradient(
        180deg,
        rgba(128, 199, 147, 0.8) 0%,
        rgba(128, 199, 147, 0) 100%
      );
  
      padding: 70px 30px 110px;
      border-radius: 34px;

      @media (max-width:991px){
        .main-title {
          font-size: 34px;
          line-height: 40px;
        }
      }
    }
  
    .item {
      padding: 25px;
      background: #ffffffd4;
      cursor: pointer;
      border-radius: 15px;
      box-shadow: 0px 20px 50px 0px #1f374e26;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: auto;
      gap: 25px;
      transition: background .4s ease-in-out, box-shadow .4s ease-in-out, height 0.4s ease-in-out;

      &:hover {
        background: #ffffff;

      }

  
      &:not(:last-child) {
        margin-bottom: 15px;
      }
  
      h4 {
        font-family: $font-medium;
        font-size: 20px;
        font-weight: 500;
        line-height: 30px;
        text-align: left;

        @media (max-width: 1199px) {
          // font-size: 20px;
          line-height: 24px;
        }
      }
  
      p {
        font-size: 15px;
        line-height: 21px;
        text-align: left;
        height: 0;
        overflow: hidden;
        transition: opacity 0.4s ease-in-out, height 0.4s ease-in-out;
      }
  
      .srv-icon {
        img {
          width: 38px;
          height: 38px;
          transition: width 0.5s ease-in-out, height 0.5s ease-in-out;
        }
      }
  
      &.expand {
        background: #ffffff;
        box-shadow: 0px 20px 50px 0px #1f374e26;
        transition: background 0.6s ease-in-out, box-shadow 0.6s ease-in-out;

        p {
          height: auto;
          transition: opacity .4s ease, height .4s ease;
        }
  
        .srv-icon {
          img {
            width: 50px;
            height: 50px;
          }
        }
      }
    }
  }
  