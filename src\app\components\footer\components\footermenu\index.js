import React from "react";
import { Container, <PERSON>, Col } from 'react-bootstrap';
import Link from 'next/link';

const FooterMenu = () => {
    return (
        <div className="footer-menu-wrap">
            <Container>
                <Row>
                    <Col className="ftr-menu-col">
                        <h3>Products</h3>
                        <ul className="footer-menu-list">
                            <li><Link href="#">Indoor Mapping</Link></li>
                            <li><Link href="#">Asset Tracking</Link></li>
                            <li><Link href="#">Proximity Services</Link></li>
                            <li><Link href="#">Analytics</Link></li>
                            <li><Link href="#">Kiosk</Link></li>
                            <li><Link href="#">Web App</Link></li>
                        </ul>
                    </Col>
                    <Col className="ftr-menu-col">
                        <h3>Industry</h3>
                        <ul className="footer-menu-list">
                            <li><Link href="#">Retail</Link></li>
                            <li><Link href="#">Airport</Link></li>
                            <li><Link href="#">Healthcare</Link></li>
                            <li><Link href="#">Logistics</Link></li>
                            <li><Link href="#">Schools & Universities</Link></li>
                            <li><Link href="#">Stadiums</Link></li>
                        </ul>
                    </Col>
                    <Col className="ftr-menu-col">
                        <h3>Company</h3>
                        <ul className="footer-menu-list">
                            <li><Link href="#">Blog</Link></li>
                            <li><Link href="#">Case Study</Link></li>
                            <li><Link href="#">Ebook</Link></li>
                        </ul>
                    </Col>
                    <Col className="ftr-menu-col">
                        <h3>Connect</h3>
                        <ul className="footer-address-list">
                            <li className="d-flex"><span className="ft-addr-head">Sales enquiry</span><span className="ft-addr-text">: 9778402640</span></li>
                            <li className="d-flex"><span className="ft-addr-head">WhatsApp</span><span className="ft-addr-text">: 9778402646</span></li>
                            <li className="d-flex"><span className="ft-addr-head">HR contact number</span><span className="ft-addr-text">: 9778414320</span></li>
                            <li className="d-flex"><span className="ft-addr-head">Email</span><span className="ft-addr-text">: <EMAIL></span></li>
                        </ul>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default FooterMenu;
