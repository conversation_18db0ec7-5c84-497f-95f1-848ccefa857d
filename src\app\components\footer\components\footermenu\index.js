import React from "react";
import { Container, Row, Col } from 'react-bootstrap';
import Link from 'next/link';

const FooterMenu = () => {
    return (
        <div className="footer-menu-wrap">
            <Container>
                <Row>
                    <Col className="ftr-menu-col">
                        <h3>Solutions</h3>
                        <ul className="footer-menu-list">
                            <li><Link href="/solutions/indoor-mapping/">Indoor Navigation</Link></li>
                            <li><Link href="/solutions/asset-tracking">Asset Tracking</Link></li>
                            <li><Link href="/solutions/proximity-services">Proximity Services</Link></li>
                            <li><Link href="/solutions/analytics">Analytics</Link></li>
                            <li><Link href="/solutions/kiosk">Kiosk</Link></li>
                            <li><Link href="/solutions/web-app">Web App</Link></li>
                        </ul>
                    </Col>
                    <Col className="ftr-menu-col">
                        <h3>Industry</h3>
                        <ul className="footer-menu-list">
                            <li><Link href="/industry/shopping-mall-indoor-navigation">Shopping Mall</Link></li>
                            <li><Link href="/industry/airport-indoor-navigation">Airport</Link></li>
                            <li><Link href="/industry/healthcare-indoor-navigation">Healthcare</Link></li>
                            <li><Link href="/industry/warehouses-indoor-navigation">Warehouses</Link></li>
                            <li><Link href="/industry/schools-universities-indoor-navigation">Schools & Universities</Link></li>
                            <li><Link href="/industry/stadiums-theme-parks-indoor-navigation">Stadiums & Theme Parks</Link></li>
                            <li><Link href="/industry/events-trade-shows-indoor-navigation">Events & Trade Shows</Link></li>
                            <li><Link href="/industry/hotels-resorts-indoor-navigation">Hotels & Resorts</Link></li>
                            <li><Link href="/industry/museums-indoor-navigation">Museums</Link></li>
                        </ul>
                    </Col>
                    <Col className="ftr-menu-col">
                        <h3>Resources</h3>
                        <ul className="footer-menu-list">
                            <li><Link href="/blog">Blog</Link></li>
                            {/* <li><Link href="#">Case Study</Link></li> */}
                            {/* <li><Link href="#">Ebook</Link></li> */}
                        </ul>
                    </Col>
                    <Col className="ftr-menu-col">
                        <h3>Connect</h3>
                        <ul className="footer-address-list">
                            <li className="d-flex"><span className="ft-addr-head">Sales enquiry</span><Link href="tel:+919778402640" className="ft-addr-text">: +91 9778 402 640</Link></li>
                            <li className="d-flex"><span className="ft-addr-head">WhatsApp</span><Link href="tel:+919778402646" className="ft-addr-text">: +91 9778 402 646</Link></li>
                            <li className="d-flex"><span className="ft-addr-head">HR contact number</span><Link href="tel:+919778413320" className="ft-addr-text">: +91 9778 414 320</Link></li>
                            <li className="d-flex"><span className="ft-addr-head">Email</span><Link href="mailto:<EMAIL>" className="ft-addr-text">: <EMAIL></Link></li>
                        </ul>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default FooterMenu;
