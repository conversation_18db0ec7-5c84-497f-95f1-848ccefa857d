import Home from "@/app/home";
import { SITE_CONFIG } from "@/lib/constants";

export const metadata = {
  metadataBase: new URL(SITE_CONFIG.url),
  title: "Indoor navigation, wayfinding, and Mapping  solution | Becomap",
  description: SITE_CONFIG.description,
  images: [SITE_CONFIG.image],
  openGraph: {
    title: "Indoor navigation, wayfinding, and Mapping  solution | Becomap",
    description: SITE_CONFIG.description,
    images: [SITE_CONFIG.image],
  },
};

export default function App() {
  return <Home />;
}
