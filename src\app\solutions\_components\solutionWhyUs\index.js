"use client";
import { useState } from "react";
import Image from "next/image";
import { Container, Row, Col } from "react-bootstrap";

const ServiceContent = ({ data, isExpanded, onClick }) => (
  <div className={`item ${isExpanded ? "expand" : ""}`} onClick={onClick}>
    <span className="srv-icon">
      <Image
        src={isExpanded ? data.activeIconUrl : data.iconUrl}
        alt={data.title}
        width={38}
        height={38}
        className="solution-icon-img"
      />
    </span>
    <div className="item-content">
      <h4 className="small-head">{data.title}</h4>
      <p className="mb-0">{data.description}</p>
    </div>
  </div>
);

const SolutionWhyUs = ({ bgImage, title, description, subImage, whyData }) => {
  const [expandedIndex, setExpandedIndex] = useState(0);

  const handleItemClick = (index) => {
    setExpandedIndex(index === expandedIndex ? null : index); // Toggle expand
  };

  return (
    <section
      className="section why-us-section pb-0"
      style={{
        backgroundImage: `url(${bgImage.src})`,
      }}
    >
      <Container>
        <Row className="justify-content-center">
          <Col xxl={10} className="why-wrapper">
            <div className="company-value-head">
              <h2 className="main-title">{title}</h2>
              <p>{description}</p>
            </div>
            <Row>
              <Col lg={7}>
                <Image
                  src={subImage}
                  alt="banner-image"
                  width="452"
                  height="368"
                  style={{ width: "100%", height: "auto" }}
                />
              </Col>
              <Col lg={5}>
                {whyData.map((item, index) => (
                  <ServiceContent
                    key={index}
                    data={item}
                    isExpanded={expandedIndex === index}
                    onClick={() => handleItemClick(index)}
                  />
                ))}
              </Col>
            </Row>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default SolutionWhyUs;
