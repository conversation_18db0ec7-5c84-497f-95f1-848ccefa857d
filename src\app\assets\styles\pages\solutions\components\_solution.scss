.sl-wrapper {
    padding: 110px 0 60px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
   .sl-details {
    h2 {
        font-size: 38px;
        line-height:44px;
        position: relative;
        z-index: 1;
        &:before {
            content: "";
            background-color: $secondary-color;
            position: absolute;
            top: 0px;
            left: -20px;
            bottom: 0;
            z-index: -1;
            width: 25%;
        }
    }
    p {
        font-size: 15px;
        line-height: 26px;
    }
   }
}