.sl-wrapper {
  padding: 110px 0 60px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;
  @media (max-width: 1199px) {
    grid-template-rows: repeat(2, 1fr);
    grid-template-columns: repeat(2, 1fr);
    padding-bottom: 0;
    > div:last-child {
      grid-area: 2 / 1 / 2 / 3;
      .sl-details {
        height: auto;
      }
    }
  }
  @media (max-width: 991px) {
    display: flex;
    flex-direction: column;
    .main-title {
      br {
        display: none;
      }
    }
  }
  .sl-details {
    @media (max-width: 1199px) {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
    h2 {
      font-size: 38px;
      line-height: 44px;
      position: relative;
      z-index: 1;
      &:before {
        content: "";
        background-color: $secondary-color;
        position: absolute;
        top: 0px;
        left: -20px;
        z-index: -1;
        width: 25%;
        height: 50%;
      }
    }
    p {
      font-size: 15px;
      line-height: 26px;
    }
  }
}

.sl-grd {
  @media (max-width: 1199px) {
    margin-top: -90px !important;
  }
  @media (max-width: 991px) {
    margin-top: 30px!important;
    gap: 20px;
  }
}
