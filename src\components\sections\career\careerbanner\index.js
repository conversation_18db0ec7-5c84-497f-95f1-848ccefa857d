import React from 'react';
import { Row, Col, Container } from 'react-bootstrap';
import Button from 'react-bootstrap/Button';
import Image from 'next/image';

const CareerBanner = () => {
    return (
        <section className='career-banner-wrap'>
            <div className='career-bannner-bg'>
                <div className='career-bannner-overlay'>
                    <Container>
                        <Row className='align-items-center'>
                            <Col md={6}>
                                <div className='career-banner-image d-flex align-items-end'>
                                    <Image src='/images/career-banner-img.png' alt='career' width="50" height="538" style={{ width: '100%' }} />
                                </div>
                            </Col>
                            <Col md={6}>
                                <div className='career-banner-details'>
                                    <h2 className='main-title mb-24'>Join our team to help build the future of indoor mapping</h2>
                                    <p className='mb-24'>Leverage our expertise in UI/UX, mobile app, eCommerce,
                                        and web den mobile app, eCommerce, and web development to take your business forward in the dynamically evolving marketplace.</p>
                                    <div className='btn-wrap'>
                                        <Button className="btn-secondary">See open positions</Button>
                                    </div>
                                </div>
                            </Col>
                        </Row>
                    </Container>
                </div>
            </div>

        </section>
    );
};

export default CareerBanner;
