"use client";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { Accordion, Col, Container, Row } from "react-bootstrap";

const IndustryAccordion = ({
  title,
  accordionData,
  videoSrc,
  description,
  image,
  bgImage,
}) => {
  const videoRef = useRef(null);
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    if (!image) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          setIsInView(entry.isIntersecting);
        },
        {
          threshold: 0.5, // Play when 50% of the video is visible
        }
      );

      const currentVideo = videoRef.current; // Store stable reference

      if (currentVideo) {
        observer.observe(currentVideo);
      }

      return () => {
        if (currentVideo) {
          observer.unobserve(currentVideo);
        }
      };
    }
  }, [image]);

  useEffect(() => {
    const currentVideo = videoRef.current; // Store stable reference

    if (currentVideo && !image) {
      if (isInView) {
        currentVideo.play().catch((error) => {
          console.error("Error attempting to play video:", error);
        });
      } else {
        currentVideo.pause();
      }
    }
  }, [isInView, image]);

  return (
    <section
      className="section accordion-section"
      style={{
        backgroundImage: `url(${bgImage.src})`,
      }}
    >
      <Container>
        <div className="company-value-head">
          <h2 className="main-title">{title}</h2>
          <p>{description}</p>
        </div>
        <Row className="gx-xxl-5 align-items-center">
          <Col xxl={7} xl={7}>
            {image ? (
              // Render image if `image` prop is provided
              <div className="im-wrapper">
                <Image
                  src={image}
                  alt={title}
                  width={720}
                  height={405} // You can adjust the dimensions as needed
                  style={{ width: "100%", height: "auto" }}
                />
              </div>
            ) : (
              // Render video if no image is provided
              <div className="video-wrapper">
                <video ref={videoRef} autoPlay={isInView} muted loop>
                  <source src={videoSrc} type="video/mp4" />
                </video>
              </div>
            )}
          </Col>
          <Col xl={5} lg={9} className="m-auto">
            <Accordion >
              {accordionData.map((item, index) => (
                <Accordion.Item eventKey={index.toString()} key={index}>
                  <Accordion.Header>{item.title}</Accordion.Header>
                  <Accordion.Body>{item.content}</Accordion.Body>
                </Accordion.Item>
              ))}
            </Accordion>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default IndustryAccordion;
