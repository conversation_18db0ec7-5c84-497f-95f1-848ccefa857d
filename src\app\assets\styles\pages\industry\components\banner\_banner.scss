.industry-banner {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-repeat: no-repeat;
  margin-top: -66px;
  position: relative;
  z-index: 1;
  min-height: 700px;
  background-size: cover;
  background-position: right;

  @media (max-width: 1399px) {
    min-height: 580px;
    margin-bottom: 0;
  }

  @media (max-width: 1199px) {
    padding: 0 30px;
    min-height: 500px;
  }

  @media (max-width: 991px) {
    padding: 0;
  }

  @media (max-width: 1088px) {
    background-position: center;

    .w-1024-100 {
      width: 100% !important;
    }
    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      pointer-events: none;
      height: 100%;
      background: radial-gradient(
        circle,
        rgb(255, 255, 255) 0%,
        rgba(255, 255, 255, 0.8) 50%,
        rgba(255, 255, 255, 0.3) 100%
      );
    }
  }

  //  &:before {
  //     content: "";
  //     position: absolute;
  //     top: 0;
  //     left: 0;
  //     width: 100%;
  //     z-index: 2;
  //     height: 100%;
  //     background: linear-gradient(180deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 1) 100%);
  //  }
  .industry-banner-details {
    color: #000;
    z-index: 3;
    position: relative;
    padding: 40px 0;
    margin-bottom: 3rem;

    @media (max-width: 1088px) {
      text-align: center;
      max-width: 585px;
      margin: 0 auto;
    }

    @media (max-width: 1399px) {
      margin-bottom: 0;
    }
    @media (max-width: 1199px) {
      padding-top: 60px;
      padding-bottom: 20px;
    }

    @media (min-width: 768px) {
      margin-right: 20%;
    }
      @media (max-width: 767px) {
      padding-top: 40px;
      padding-bottom: 40px;
    }

    .main-title {
      font-family: $font-bold;
      @media (max-width: 1399px) {
        font-size: 36px;
        line-height: 45px;
      }
      @media (max-width: 1199px) {
        font-size: 32px;
        line-height: 38px;
      }
      @media (max-width: 1088px) {
        br {
          display: none;
        }
      }
      @media (max-width: 767px) {
        font-size: 24px;
        line-height: 30px;
      }
    }

    p {
      line-height: 25px;
      @media (max-width: 1199px) {
        br {
          display: none;
        }
        padding-right: 25%;
      }

      @media (max-width: 1088px) {
        padding-right: 0;
      }

      @media (max-width: 767px) {
        font-size: 14px;
        line-height: 20px;
      }
    }

    button {
      min-width: 190px;

      @media (max-width: 767px) {
        font-size: 14px!important;
        line-height: 13px;
        height: auto;
        padding: 13px 20px;
        min-width: auto;
      }
    }
  }
}
