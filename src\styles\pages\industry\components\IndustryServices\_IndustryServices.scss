.service-section {
  @media (max-width: 767px) {
    padding: 50px 30px!important;
  }
  @media (max-width: 767px) {
    .ser-cr {
      background: #ffffff;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 30px;
      box-shadow: 0 5px 80px 0 #dce1ea;
    }
  }
  .service-item-content {
    padding-right: 20%;
    padding-left: 0;

    @media (max-width: 1600px) {
      padding-right: 15%;
    }
    @media (max-width: 1490px) {
      padding-right: 10%;
      padding-left: 5%;
    }
    @media (max-width: 1199px) {
      padding-right: 0;
    }
    @media (max-width: 991px) {
      text-align: center !important;
      padding: 0;
      position: absolute;
      top: 50%;
      left: 10%;
      right: 10%;
      transform: translateY(-50%);
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      max-width: 100%;
      height: fit-content;
      background: #ffffff;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 5px 80px 0 #dce1ea;
    }
    @media (max-width: 767px) {
      position: unset;
      box-shadow: none;
      border-radius: 0;
      transform: unset;
      margin-top: -30px;
    }
    //   margin-top: -100px;
    //   position: relative;
    //   z-index: 1;
    //   margin-bottom: 150px;
    // }
    .srv-icon {
      background-color: $primary-color;
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      margin-bottom: 18px;
      @media (max-width: 1490px) {
        width: 50px;
        height: 50px;
      }
      @media (max-width: 1199px) {
        margin-bottom: 10px;
        width: 45px;
        height: 45px;
      }
    }
    p {
      line-height: 26px;
      @media (max-width: 1199px) {
        line-height: 24px;
      }
    }
    .small-head {
      font-family: $font-bold;
      font-size: 30px;
      font-weight: 700;
      line-height: 38px;
      margin-bottom: 15px;
      @media (max-width: 1490px) {
        font-size: 28px;
        line-height: 33px;
      }
      @media (max-width: 1310px) {
        font-size: 24px;
        line-height: 28px;
        margin-bottom: 5px;
      }
    }
    &.reverse-content {
      padding-left: 20%;
      padding-right: 0;

      @media (max-width: 1600px) {
        padding-left: 15%;
      }
      @media (max-width: 1490px) {
        padding-left: 10%;
        padding-right: 5%;
      }
      @media (max-width: 1199px) {
        padding-left: 0;
      }
      @media (max-width: 991px) {
        padding: 30px;
      }
    }
  }

  .im-wrapper {
    overflow: hidden;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      background: linear-gradient(
        90deg,
        #ffffff 2.71%,
        rgba(255, 255, 255, 0) 55.67%
      );
      width: 100%;
      height: 100%;
      z-index: 1;
      pointer-events: none;
      @media (max-width: 991px) {
        top: auto;
        bottom: 0;
        right: 0;
        background: rgba(52, 168, 83, 0.4);

        // background: linear-gradient(0, #FFFFFF 16%, rgba(255, 255, 255, 0) 55.67%);
      }
      @media (max-width: 767px) {
        background: linear-gradient(
          0,
          #ffffff 16%,
          rgba(255, 255, 255, 0) 55.67%
        )!important;
      }
    }

    &.reverse-img {
      &::before {
        background: linear-gradient(
          -90deg,
          #f3f3f3 2.71%,
          rgba(255, 255, 255, 0) 55.67%
        );
        @media (max-width: 991px) {
          top: auto;
          bottom: 0;
          right: 0;
          // background: linear-gradient(0, #F3F3F3 16%, rgba(255, 255, 255, 0) 55.67%);
        }
        @media (max-width: 767px) {
          background: linear-gradient(
            0,
            #ffffff 16%,
            rgba(255, 255, 255, 0) 55.67%
          );
        }
      }
    }
  }
  &.solutn {
    .im-wrapper {
      &::before {
        background: linear-gradient(
          90deg,
          #f3f3f3 2.71%,
          rgba(255, 255, 255, 0) 55.67%
        );
      }

      &.reverse-img {
        &::before {
          background: linear-gradient(
            -90deg,
            #ffffff 2.71%,
            rgba(255, 255, 255, 0) 55.67%
          );
        }
      }
      &.no-befre {
        &::before {
          content: none;
        }
      }
    }
  }
  .service-timeline {
    ul {
      list-style: none;
      margin: 0;
      padding: 0;
      position: relative;
      padding-right: 6%;
      @media (max-width: 1199px) {
        padding-right: 0;
      }
      &::before {
        content: "";
        position: absolute;
        top: 20px;
        bottom: 20px;
        left: 0;
        width: 41px;
        border-right: 2px dashed $secondary-color;
        z-index: -1;
        @media (max-width: 1199px) {
          top: 35px;
          bottom: 35px;
        }
        // background: #ddd;
      }
      li {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 26px;
        &:not(:last-child) {
          margin-bottom: 60px;
        }
        .icon-sec {
          width: 82px;
          height: 82px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50px;
          background: $secondary-color;
          flex: 1 0 auto;
        }
        .content-sec {
          h3 {
            font-size: 20px;
            font-weight: 700;
            line-height: 38px;
            margin: 0;
          }
          p {
            margin: 0;
            font-size: 16px;
            line-height: 26px;
          }
        }
      }
    }
  }
}

.service-extra-section {
  padding: 50px 0;
  @media (max-width: 991px) {
    .row {
      justify-content: center !important;
    }
  }
  .service-item-content {
    padding: 0;
    @media (max-width: 991px) {
      text-align: center !important;
      position: unset;
      transform: unset;
      max-width: 100%;
      height: unset;
      background: #ffffff;
      padding: 0px;
      border-radius: 0px;
      box-shadow: none;
    }
    .small-head {
      font-size: 36px !important;
      line-height: 40px !important;
      padding: 0;
      @media (max-width: 991px) {
        br {
          display: none;
        }
        margin-top: 30px;
      }
      @media (max-width: 1199px) {
        font-size: 28px !important;
        line-height: 33px !important;
        margin-bottom: 5px !important;
      }
    }
  }
}
