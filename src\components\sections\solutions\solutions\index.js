"use client";
import ClientSlider from "@/app/company/_components/companybanner/components/clients";

import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { Col, Container, Row } from "react-bootstrap";

const SolutionItem = ({ title, description, iconUrl, color }) => (
  <Col lg="4">
    <div className="solution-item-box text-center">
      <div className="solution-icon-wrap d-flex flex-column align-items-center text-center w-100">
        <span
          className={`srv-icon-bg mb-4`}
          style={{
            backgroundColor: `${color}`,
          }}
        >
          <Image
            src={iconUrl}
            alt={title}
            width={28}
            height={28}
            className="solution-icon-img "
          />
        </span>
        <h4
          className="small-head w-100 mb-3"
          style={{
            fontSize: "24px",
          }}
        >
          {title}
        </h4>
      </div>
      <p
        className="mb-0"
        style={{
          fontSize: "15px",
          lineHeight: "24px",
        }}
      >
        {description}
      </p>
    </div>
  </Col>
);
const IndustrySolutions = ({
  title,
  image,
  solutions,
  description,
  imageProperty,
  videoSrc,
}) => {
  const videoRef = useRef(null);
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    if (!image) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          setIsInView(entry.isIntersecting);
        },
        {
          threshold: 0.5, // Play when 50% of the video is visible
        }
      );

      const currentVideo = videoRef.current; // Store stable reference

      if (currentVideo) {
        observer.observe(currentVideo);
      }

      return () => {
        if (currentVideo) {
          observer.unobserve(currentVideo);
        }
      };
    }
  }, [image]);
  return (
    <section className="section pt-0 bg-bc-light">
      <Container>
        <div className="sl-wrapper">
          <div>
            <div className="sl-details">
              <h2
                className="main-title mb-3"
                dangerouslySetInnerHTML={{ __html: title }}
              />
              <p className="mb-0">{description.part1}</p>
            </div>
          </div>
          <div>
            <div
              className={`d-flex d-flex align-items-center justify-content-center sl-video`}
              style={{
                width: "100%",
                position: "relative",
                height: "100%",
              }}
            >
              <video
                ref={videoRef}
                autoPlay={isInView}
                muted
                id="sl-video"
                loop
              >
                <source src={"/videos/solution.mp4"} type="video/mp4" />
              </video>
              {/* <Image
                src={image.src}
                alt="banner-image"
                width={542}
                height={447}
                style={{
                  objectFit: "contain",
                  width: "100%",
                  height: "auto",
                }}
              /> */}
            </div>
          </div>
          <div>
            <div className="sl-details">
              <p className="">{description.part2}</p>
              <p className="mb-0">{description.part3}</p>
            </div>
          </div>
        </div>
        <Row className="sl-grd">
          {solutions.map((item, index) => (
            <SolutionItem
              key={index}
              title={item.title}
              description={item.description}
              iconUrl={item.iconUrl}
              color={item.color}
            />
          ))}
        </Row>
      </Container>
    </section>
  );
};

export default IndustrySolutions;
