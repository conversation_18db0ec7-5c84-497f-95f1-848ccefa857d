import React, { useState } from 'react';
import { Row, Col, Form, Button } from 'react-bootstrap';
import Image from 'next/image';

const PositionContent = ({ title }) => {
    const [fileName, setFileName] = useState('No file chosen, yet.');

    const handleFileChange = (e) => {
        setFileName(e.target.files[0] ? e.target.files[0].name : 'No file chosen, yet.');
    };
    return (
        <div className='position-content-wrap'>
            <h3 className='position-title mb-4'>{title}</h3>
            <Row className="job-detail">
                <Col>
                    <div className='job-detail-topbox d-flex align-items-center mb-5'>
                        <div className="job-icon">
                            <Image src='/images/icons/experience-icon.png' alt='career' width="26" height="26" />
                        </div>
                        <div className='job-content'>
                            <h5>Experience</h5>
                            <h4>1-3</h4>
                        </div>
                    </div>
                </Col>
                <Col>
                    <div className='job-detail-topbox d-flex align-items-center'>
                        <div className="job-icon">
                            <Image src='/images/icons/position-count-icon.png' alt='career' width="26" height="26" /></div>
                        <div className='job-content'>
                            <h5>No. of positions</h5>
                            <h4>5</h4>
                        </div>
                    </div>
                </Col>
                <Col>
                    <div className='job-detail-topbox d-flex align-items-center'>
                        <div className="job-icon">
                            <Image src='/images/icons/location.png' alt='career' width="26" height="26" /></div>
                        <div className='job-content'>
                            <h5>Location</h5>
                            <h4>Kochi</h4>
                        </div>
                    </div>
                </Col>
            </Row>
            <div className='requirement-wrap'>
                <h4 className='position-sub-title'>Requirements</h4>
                <ul className="requirements">
                    <li>
                        <span className="check-icon"><svg width="19" height="15" viewBox="0 0 19 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.7438 14.25L0.331299 7.83755L1.93442 6.23442L6.7438 11.0438L17.0657 0.721924L18.6688 2.32505L6.7438 14.25Z" fill="#E8EAED" />
                        </svg>
                        </span>Being proactive to achieve team goals and good leadership skills
                    </li>
                    <li>
                        <span className="check-icon"><svg width="19" height="15" viewBox="0 0 19 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.7438 14.25L0.331299 7.83755L1.93442 6.23442L6.7438 11.0438L17.0657 0.721924L18.6688 2.32505L6.7438 14.25Z" fill="#E8EAED" />
                        </svg>
                        </span>Must be detail oriented and an active listener
                    </li>
                    <li>
                        <span className="check-icon"><svg width="19" height="15" viewBox="0 0 19 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.7438 14.25L0.331299 7.83755L1.93442 6.23442L6.7438 11.0438L17.0657 0.721924L18.6688 2.32505L6.7438 14.25Z" fill="#E8EAED" />
                        </svg>
                        </span>Ability to work under pressure and confidence to take up ambitious goals
                    </li>
                </ul>
            </div>
            <h4 className='position-sub-title'>Apply for Business Development Executive</h4>
            <Form className="apply-section talkto-form-wrap">
                <Row>
                    <Col md={6}>
                        <Form.Group className="form-group">
                            <Form.Control type="text" placeholder="Name" className="form-control" />
                        </Form.Group>
                    </Col>
                    <Col md={6}>
                        <Form.Group className="form-group">
                            <Form.Control type="email" placeholder="Email Address" className="form-control" />
                        </Form.Group>
                    </Col>
                    <Col md={12}>
                        <div className="upload-button form-group">
                            <label className="custom-file-upload">
                                <input type="file" onChange={handleFileChange} />
                                Upload Resume
                            </label>
                            <span className="file-info">{fileName}</span>
                        </div>
                    </Col>
                </Row>
                <div className='creer-form-footer'>
                    <Button className="btn-secondary">Apply Now</Button>
                </div>


            </Form>
        </div>
    );
};

export default PositionContent;
