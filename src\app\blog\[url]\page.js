import Image from "next/image";
import { getCarouselBlog } from "@/lib/actions";
import BlogDetails from "@/components/sections/blog/blogDetails";

export async function generateStaticParams() {
  try {
    const posts = await getCarouselBlog(1, 10000);

    const slug = posts.results.map((post) => ({
      url: post.url,
    }));

    return slug;
  } catch (error) {
    console.error("Error fetching posts:", error);
    throw error;
  }
}

const SinglePage = ({ params }) => {
  const { url } = params;
  return (
    <main className="blog-post mt-5" itemScope itemType="http://schema.org/Article">
      <BlogDetails slug={url} />
    </main>
  );
};

export default SinglePage;
