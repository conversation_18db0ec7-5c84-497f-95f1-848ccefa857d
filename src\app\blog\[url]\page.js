import Image from "next/image";
import axios from "axios";
import BlogDetails from "../_components/blogDetails";

export async function generateStaticParams() {
  try {
    const response = await axios.get(
      "https://blog-api.becomap.com/posts/?page=1&page_size=10000",
      {
        headers: {
          "X-Blog-Identifier": "beco",
        },
      }
    );

    const posts = response.data;
    
    const slug = posts.results.map((post) => ({
      url: post.url,
    }));

    return slug;
  } catch (error) {
    console.error("Error fetching posts:", error);
    throw error;
  }
}

const SinglePage = ({ params }) => {
  const { url } = params;
  return (
    <main className="blog-post mt-5" itemScope itemType="http://schema.org/Article">
      <BlogDetails slug={url} />
    </main>
  );
};

export default SinglePage;
