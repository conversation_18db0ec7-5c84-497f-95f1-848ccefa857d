import axios from "axios";
import CategoryList from "@/app/blog/_components/categoryList/categoryList";
import TalkToOurTeam from "@/app/components/talktoourteam";
export async function generateStaticParams() {
  try {
    const response = await axios.get(
      "https://blog-api.becomap.com/posts/?page=1&page_size=10000",
      {
        headers: {
          "X-Blog-Identifier": "beco",
        },
      }
    );

    const posts = response.data;

    const slug = posts.results.map((post) => ({
      slug: post.category.slug,
    }));

    return slug;
  } catch (error) {
    console.error("Error fetching posts:", error);
    throw error;
  }
}


const Blog = ({ params }) => {

  const { slug } = params;
  return (
    <div className="blog-page-wrap">
      <CategoryList slug={slug} />
      <div>
        <TalkToOurTeam />
      </div>
    </div>
  );
};

export default Blog;
