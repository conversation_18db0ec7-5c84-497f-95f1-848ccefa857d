"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import Link from "next/link";
import Image from "next/image";
import Container from 'react-bootstrap/Container';
import { Autoplay} from 'swiper/modules';
const ClientSlider = () => {
  return (
    <div className="cleientslider-wrap">
        <Container>
      <h5 className="sub-title">
        MILLIONS OF PEOPLE VISITING THE WORLD’S LARGEST VENUES USE BECOMAP
      </h5>
      <Swiper
        slidesPerView={7}
        spaceBetween={30}
        className="clientslider"
        loop={true}
        autoplay={{
          delay: 2500,
          disableOnInteraction: false,
        }}
        modules={[Autoplay]}
        breakpoints={{
          1200: {
            slidesPerView: 7,
          },
          992: {
            slidesPerView: 5,
          },
          768: {
            slidesPerView: 3,
          },
          0: {
            slidesPerView: 2,
          }
        }}
      >
        <SwiperSlide>
          <Link href="#">
            <Image
              src="/images/clientimages/hi-LITE-logo.png"
              alt="clientimage"
              width="145"
              height="81"
            />
          </Link>
        </SwiperSlide>
        <SwiperSlide>
          <Link href="#">
            <Image src="/images/clientimages/jio-logo.png" alt="clientimage"  width="145"
              height="81" />
          </Link>
        </SwiperSlide>
        <SwiperSlide>
          <Link href="#">
            <Image
              src="/images/clientimages/city-center-mall-logo.png"
              alt="clientimage"
              width="145"
              height="81"
            />
          </Link>
        </SwiperSlide>
        <SwiperSlide>
          <Link href="#">
            <Image
              src="/images/clientimages/global-village-logo.png"
              alt="clientimage"
              width="145"
              height="81"
            />
          </Link>
        </SwiperSlide>
        <SwiperSlide>
          <Link href="#">
            <Image
              src="/images/clientimages/honeywell-logo.png"
              alt="clientimage"
              width="145"
              height="81"
            />
          </Link>
        </SwiperSlide>
        <SwiperSlide>
          <Link href="#">
            <Image
              src="/images/clientimages/forum-logo.png"
              alt="clientimage"
              width="145"
              height="81"
            />
          </Link>
        </SwiperSlide>
        <SwiperSlide>
          <Link href="#">
            <Image
              src="/images/clientimages/appollo-logo.png"
              alt="clientimage"
              width="145"
              height="81"
            />
          </Link>
        </SwiperSlide>
        <SwiperSlide>
          <Link href="#">
            <Image
              src="/images/clientimages/hi-LITE-logo.png"
              alt="clientimage"
              width="145"
              height="81"
            />
          </Link>
        </SwiperSlide>
        <SwiperSlide>
          <Link href="#">
            <Image src="/images/clientimages/jio-logo.png" alt="clientimage"  width="145"
              height="81" />
          </Link>
        </SwiperSlide>
      </Swiper>
      </Container>
    </div>
  );
};

export default ClientSlider;
