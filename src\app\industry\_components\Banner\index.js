import Image from "next/image";
import { <PERSON><PERSON>, Col, Container, Row } from "react-bootstrap";

const IndustryBanner = ({ bgImage, title, description }) => {
  return (
    <section
      className="industry-banner"
      style={{
        backgroundImage: `url(${bgImage?.src})`,
      }}
    >
      <Container fluid={"xl"}>
        <Row className="align-items-center">
          <Col md={6} className="w-1024-100">
            <div className="industry-banner-details">
              <h2 className="main-title mb-2 mb-md-3" dangerouslySetInnerHTML={{ __html: title }}></h2>
              <p dangerouslySetInnerHTML={{ __html: description }}/>
              <div className="btn-wrap mt-3 mt-md-4">
                {/* eslint-disable react/no-unescaped-entities */}

                <Button className="btn-secondary me-3">
                  Let's talk to Sales
                </Button>
                <Button className="btn btn-outline">See Demo</Button>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default IndustryBanner;
