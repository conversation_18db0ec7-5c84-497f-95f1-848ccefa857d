"use client"
import { useEffect, useState } from 'react';
import Container from 'react-bootstrap/Container';
import Image from 'next/image';
import Headermenu from "./components/headermenu";
import HeaderSearch from "./components/headersearch";
import Button from 'react-bootstrap/Button';
import Link from 'next/link';


function Header() {
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const [isSticky, setIsSticky] = useState(false);

  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen);
  };

  const handleScroll = () => {
    const scrollPosition = window.scrollY;
    setIsSticky(scrollPosition > 50); // Adjust the value as needed for when the header should stick
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const [menuOpen, setMenuOpen] = useState(false);

  useEffect(() => {
    document.body.style.overflow = menuOpen ? "hidden" : "";

    return () => {
      document.body.style.overflow = "";
    };
  }, [menuOpen]);

  return (
    <div className={`header-wrap ${isSticky ? 'sticky' : ''} ${menuOpen ? 'menu-open' : ''}`}>
        <Container fluid={"xl"}>
          <div className="d-flex header-cont justify-content-between align-items-center">
            <div className="header-left">
              <Link href="/">
                <Image
                  src="/images/logo.png"
                  alt="beco logo"
                  width={159}
                  height={28}
                  className="beco-logo"
                />
              </Link>
            </div>
            <div className="header-section-wrap d-flex align-items-center">
            <div className={`header-menu d-flex order-lg-1 order-2 ${isSearchOpen ? 'menu-hidden' : ''}`}>
              <Headermenu menuOpen={menuOpen} setMenuOpen={setMenuOpen}  />
              </div>

              <div className="header-right d-flex order-lg-2 order-1">
              <Link href="/contact" className="border-btn medium-btn header-right-child">Contact us</Link>
                {/* <div className="language-select-wrap header-right-child">
                <span>EN</span>
              </div>
              <div className="header-right-child">
                <HeaderSearch isSearchOpen={isSearchOpen} toggleSearch={toggleSearch} />
              </div> */}
              </div>
            </div>
          </div>
        </Container>
      </div>
  );
}

export default Header;
