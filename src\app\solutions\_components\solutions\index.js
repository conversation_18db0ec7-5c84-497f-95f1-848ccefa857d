import ClientSlider from "@/app/company/_components/companybanner/components/clients";
import Image from "next/image";
import { Col, Container, Row } from "react-bootstrap";

const SolutionItem = ({ title, description, iconUrl, color }) => (
  <Col md="4">
    <div className="solution-item-box text-center">
      <div className="solution-icon-wrap d-flex flex-column align-items-center text-center w-100">
        <span
          className={`srv-icon-bg mb-4`}
          style={{
            backgroundColor: `${color}`,
          }}
        >
          <Image
            src={iconUrl}
            alt={title}
            width={28}
            height={28}
            className="solution-icon-img "
          />
        </span>
        <h4 className="small-head w-100 mb-3" style={{
          fontSize: "24px",
        }}>{title}</h4>
      </div>
      <p className="mb-0" style={{
        fontSize: "15px",
        lineHeight: "24px",
      }}>{description}</p>
    </div>
  </Col>
);
const IndustrySolutions = ({
  title,
  image,
  solutions,
  description,
  imageProperty,
}) => {
  return (
    <section className="section pt-0 bg-bc-light">
      <Container>
        <div className="sl-wrapper">
          <div>
            <div className="sl-details">
              <h2 className="main-title mb-3">{title}</h2>
              <p className="mb-0">{description.part1}</p>
            </div>
          </div>
          <div>
            <div
              className={`d-flex d-flex align-items-center justify-content-center sl-img`}
              style={{
                width: "100%",
                position: "relative",
                height: "100%",
              }}
            >
              <Image
                src={image.src}
                alt="banner-image"
                width={542}
                height={447}
                style={{
                  objectFit: "contain",
                  width: "100%",
                  height: "auto",
                }}
              />
            </div>
          </div>
          <div>
            <div className="sl-details">
              <p className="">{description.part2}</p>
              <p className="mb-0">{description.part3}</p>
            </div>
          </div>
        </div>
        <Row>
          {solutions.map((item, index) => (
            <SolutionItem
              key={index}
              title={item.title}
              description={item.description}
              iconUrl={item.iconUrl}
              color={item.color}
            />
          ))}
        </Row>
      </Container>
    </section>
  );
};

export default IndustrySolutions;
