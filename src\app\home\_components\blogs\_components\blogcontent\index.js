import React from "react";
import Link from "next/link";
import Image from "next/image";
import Button from 'react-bootstrap/Button';
import Card from 'react-bootstrap/Card';
import { truncate } from "@/app/utils/truncate";

const BlogContent = ({ writer, title, content, imgUrl,slug }) => {
    
    return (
        <div className="blog-card-wrap" >
            <Card className="blog-card">
                <div className="blog-img-wrap">
                <Image
                        src={imgUrl}
                        alt="blog image"
                        layout="responsive"
                        width={400}  
                        height={300} 
                    />
                </div>
                <Card.Body>
                    <div className="writer-mention">{writer}</div>
                    <Card.Title>{truncate(title, 40)}</Card.Title>
                    <Card.Text>
                        {truncate(content, 70)}
                    </Card.Text>
                    <Button variant="primary" className="border-btn medium-btn">
                    <Link href={`/blog/${slug}`}>View details</Link></Button>
                </Card.Body>
            </Card>
        </div>
    );
};

export default BlogContent;
