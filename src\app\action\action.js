import axios from "axios";

// CREATE TABLE todos (
//   id SERIAL PRIMARY KEY,
//   text TEXT NOT NULL
// );

export async function formSubmit(formData, contactIdentifier) {
  try {
    const response = await axios.post(
      "https://blog-api.becomap.com/contact-beco/",
      formData,
      {
        headers: {
          "Content-Type": "application/json",
          "X-Contact-Identifier": contactIdentifier,
        },
      }
    );
    return response.status;
  } catch (error) {
    console.error("Error submitting form:", error);
    return error.response ? error.response.status : 500;
  }
}

export async function downlaodFormSubmit(formData) {
  try {
    const response = await axios.post(
      "https://blog-api.becomap.com/beco-downloadscontacts/",
      formData,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    return response.status;
  } catch (error) {
    console.error("Error submitting form:", error);
    return error.response ? error.response.status : 500;
  }
}

export async function subscribFormSubmit(formData) {
  try {
    const response = await axios.post(
      "https://blog-api.becomap.com/subscribe-beco/",
      formData,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    return response.status;
  } catch (error) {
    console.error("Error submitting form:", error);
    return {
      status: error.response ? error.response.status : 500,
      data: error.response
        ? error.response.data
        : { message: "Form submission failed" },
    };
  }
}

export async function getCarouselBlog(page, page_size) {
  try {
    const response = await axios.get("https://blog-api.becomap.com/posts/", {
      headers: {
        "X-Blog-Identifier": "beco",
      },
      params: {
        page: page,
        page_size: page_size,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error submitting form:", error);
    return error.response ? error.response.status : 500;
  }
}

export async function getBlogSearch(searchTerm) {
  try {
    const response = await axios.get(`https://blog-api.becomap.com/posts/`, {
      headers: {
        "X-Blog-Identifier": "beco",
      },
      params: {
        search: searchTerm,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error submitting form:", error);
    return error.response ? error.response.status : 500;
  }
}
