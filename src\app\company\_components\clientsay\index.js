"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import Container from 'react-bootstrap/Container';
import ClientSayContent from "./components/clientsaycontent";
import { Autoplay} from 'swiper/modules';
const ClientSayCompany = () => {
  // Dummy data array
  const ClientSayData = [
    {
      writer: "Nashik City Centre",
      position: "Nashik, Maharashtra",
      content: "beComap helped improve our mall’s navigation. Their indoor navigation & Mall directory software made it easy for visitors to find stores and promotions, transforming the overall experience.",
      imgUrl: "/images/city-center-mall-nashik.png"
    },
    {
      writer: "Global Village",
      position: "Dubai",
      content: "Introducing be<PERSON><PERSON><PERSON>'s GPS navigation at Global Village has been a success. The indoor navigation ensures a smooth experience. Visitors now explore with ease, boosting participation and engagement in events and promotions.",
      imgUrl: "/images/global-village.png"
    },
    {
      writer: "Forum Mall",
      position: "Kochi, Kerala",
      content: "beCo's kiosk software has significantly improved the shopping experience at Forum Mall, Kochi. Shoppers easily access offers, stay updated on events, and provide feedback, boosting customer satisfaction.",
      imgUrl: "/images/forum-mall.jpg"
    },
    {
      writer: "HiLite Mall",
      position: "Kozhikode, Kerala",
      content: "To improve convenience and interactivity for our customers, we introduced indoor navigation. Partnering with beCo has been a great decision, we're pleased to see our customers enjoying the improved shopping experience.",
      imgUrl: "/images/hilite-mall.jpg"
    }    
  ];

  return (
    <div className="section client-say-section-wrap">
      <Container>
        <div className="section-head text-center">
          <h2 className="main-title mb-lg-5 mb-2">
          What Our Clients Say About Us
          </h2>
        </div>
      </Container>
      <Swiper
        slidesPerView={"auto"}
        // spaceBetween={30}
      
        className="clientsayslider"
        // centeredSlides={true}
        // loop={true}
        // autoplay={{
        //   delay: 25555555500,
        //   disableOnInteraction: false,
        // }}
        // modules={[Autoplay]}
        // speed={2000000000000}
        breakpoints={{
          768: {
            slidesPerView: 1,
          }
        }}
      >
        {ClientSayData.map((client, index) => (
          <SwiperSlide key={index} style={{
            // maxWidth: "476px",
          }}>
            <ClientSayContent writer={client.writer} position={client.position} content={client.content} imgUrl={client.imgUrl} />
          </SwiperSlide>
        ))}
      </Swiper>

    </div>
  );
};

export default ClientSayCompany;

