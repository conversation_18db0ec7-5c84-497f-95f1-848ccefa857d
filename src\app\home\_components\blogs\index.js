"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import Container from "react-bootstrap/Container";
import BlogContent from "./_components/blogcontent";
import { Autoplay } from "swiper/modules";
import { getCarouselBlog } from "@/app/action/action";
import { useEffect, useState } from "react";

const Blogs = () => {
  const [blogData, setBlogData] = useState([]);
 
  // const blogData = [
  //   {
  //     writer: "DEVELOPER",
  //     title: "Visio Move Essential 2.0.0: Unveiling New Heights in Mobile Mapping",
  //     content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  //     imgUrl: "/images/blog-img-1.png"
  //   },
  //   {
  //     writer: "Managing Director",
  //     title: "Visio Move Essential 2.0.0: Unveiling New Heights in Mobile Mapping",
  //     content: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
  //     imgUrl: "/images/blog-img-2.png"
  //   },
  //   {
  //     writer: "Managing Director",
  //     title: "Visio Move Essential 2.0.0: Unveiling New Heights in Mobile Mapping",
  //     content: "Alleviate confusion by helping users find their way to desired destinations within your campus...",
  //     imgUrl: "/images/blog-img-1.png"
  //   },
  //   {
  //     writer: "DEVELOPER",
  //     title: "Visio Move Essential 2.0.0: Unveiling New Heights in Mobile Mapping...",
  //     content: "Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
  //     imgUrl: "/images/blog-img-2.png"
  //   },
  //   {
  //     writer: "Managing Director",
  //     title: "Visio Move Essential 2.0.0:",
  //     content: "Alleviate confusion by helping users find their way to desired destinations within your campus...",
  //     imgUrl: "/images/blog-img-1.png"
  //   },
  //   {
  //     writer: "DEVELOPER",
  //     title: "Blog Title 4",
  //     content: "Visio Move Essential 2.0.0: Unveiling New Heights in Mobile Mapping",
  //     imgUrl: "/images/blog-img-2.png"
  //   }
  // ];

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    const dataBog = await getCarouselBlog(1, 20);
    setBlogData(dataBog.results);
  };

  return (
    <div className="section blog-section-wrap">
      {blogData ? <>
      <Container>
        <div className="section-head text-center">
          <h2 className="main-title mb-5">Read our Latest blogs</h2>
        </div>
      </Container>
      <Swiper
        slidesPerView={"auto"}
        className="blogslider"
        centeredSlides={true}
        loop={true}
        autoplay={{
          delay: 2500,
          disableOnInteraction: false,
        }}
        modules={[Autoplay]}
        speed={2000}
        // breakpoints={{
        //   768: {
        //     slidesPerView: 1,
        //   },
        // }}
      >
        {blogData?.map((blog, index) => (
          <SwiperSlide key={index}>
            <BlogContent
              writer={blog.author.name}
              title={blog?.title}
              content={blog.description}
              imgUrl={blog.image}
              slug={blog.url}
            />
          </SwiperSlide>
        ))}
      </Swiper>
      </> : null}
    </div>
  );
};

export default Blogs;
