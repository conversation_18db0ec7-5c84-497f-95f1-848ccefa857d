import ClientSlider from "@/app/company/_components/companybanner/components/clients";
import SolutionBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
// import IndustryExperience from "../_components/IndustryExperience";
// import IndustrySecondSection from "../_components/IndustrySecondSection";
import IndustrySolutions from "../_components/solutions";
// import IndustryAccordion from "../_components/IndustryAccordion";
import IndustryServices from "../_components/IndustryServices";
import IndustryEBook from "../_components/IndustryEBook";
import FaqSection from "../_components/FaqSection";
import SolutionWhyUs from "../_components/solutionWhyUs";
import HeroImg from "@/app/assets/images/solutions/indoor-hero.png";
import SecImg from "@/app/assets/images/solutions/becomap-care.png";
// import SecImg2 from "@/app/assets/images/industry/mall-sec-im-2.png";
// import SecImg3 from "@/app/assets/images/industry/mall-help-illu.png";
// import SecImg4 from "@/app/assets/images/industry/mall-im-sec-2-2.png";
// import BgImg from "@/app/assets/images/industry/indu-accr-bg.png";
import TalkToOurTeam from "@/app/components/talktoourteam";
import Blogs from "@/app/home/<USER>/blogs";
import { Col, Container, Row } from "react-bootstrap";
import Image from "next/image";

import SolutionWhyUsBg from "@/app/assets/images/solutions/why-bg.png";
import WhySubImage from "@/app/assets/images/solutions/indoor/why-sub-img.png";
import SolutionsServices from "../_components/IndustryServices";
export const metadata = {
  metadataBase: new URL("https://becomap.com"),
  title: "Indoor Navigation system | Becomap",
  description:
    "Discover seamless indoor navigation solutions with Becomap. Enhance user experiences in malls, airports, & more with precise, real-time indoor mapping technology!",
  images: ["/becomap.png"],
  openGraph: {
    title: "Indoor Navigation system | Becomap",
    description:
      "Discover seamless indoor navigation solutions with Becomap. Enhance user experiences in malls, airports, & more with precise, real-time indoor mapping technology!",
    images: ["/becomap.png"],
  },
};  
const mallNavigationFAQ = [
  {
    question: "What are the main benefits of using indoor mapping for my business?",
    answer: "Indoor mapping improves navigation, enhances customer experience, optimizes space usage, and provides valuable insights into foot traffic, helping businesses improve operations and decision-making."
  },
  {
    question: "Can indoor mapping be customized for different types of buildings?",
    answer: "Yes, indoor mapping can be tailored to fit any type of indoor space, from shopping malls and office buildings to hospitals, universities, and large event venues."
  },
  {
    question: "How long does it take to implement an indoor mapping solution?",
    answer: "The implementation timeline varies depending on the size and complexity of the space, but typically it takes 4-8 weeks to complete the mapping and integration into your systems."
  }
];

const SOLUTIONS = [
  {
    title: "Mapping",
    description:
      "We create clear maps of your entire space, showing everything from rooms and hallways to key areas like restrooms and service desks. These maps help visitors find their way easily, even in big or complicated buildings. Whenever there are changes, we keep the maps updated so visitors always know where to go.",
    iconUrl: "/images/icons/route.png",
    color: "#34A853",
  },
  {
    title: "Sensors and Positioning",
    description:
      "Using QR codes and URLs, we help visitors know where they are inside the building. This makes it easier for them to get around without getting lost.",
    iconUrl: "/images/icons/wifi.png",
    color: "#34A853",
  },
  {
    title: "Navigation Interface",
    description:
      "Visitors can access the navigation through a mobile app, web app, or kiosk. The interface is simple and gives step-by-step directions. Along the way, they can also find useful info, like nearby restrooms or event details, making their visit easier and more enjoyable.",
    iconUrl: "/images/icons/location-2.png",
    color: "#34A853",
  },
];

const accordionData = [
  {
    title: "Easily Integrate Maps",
    content:
      "Integrate map of your retail space into existing apps to effortlessly guide shoppers to their favorite stores, restaurants, and services via the shortest route.",
  },
  {
    title: "Multi-Language Support",
    content:
      "Make your mall map accessible to all visitors by offering it in multiple languages. beComap allows you to provide directions and information in the shopper’s preferred language, making the mall experience more personalized and user-friendly. This feature is particularly valuable in diverse or tourist-heavy locations.",
  },
  {
    title: "Give Updates on Store Offers",
    content:
      "Keep shoppers in the loop with instant updates on store deals, upcoming events, and essential services, improving their overall shopping experience.",
  },
  {
    title: "Less Searching, More Shopping",
    content:
      "Navigate in a flash with our system—find the fastest route and shortest path from parking to stores, or multi-stop routes to plan your shopping with less walking and more items on your cart.",
  },
];

const ServiceData = [
  {
    title: "1. Guidance in Complex Spaces",
    content:
      "Large venues like hospitals, airports, and office buildings can be tough to navigate, especially for first-time visitors. Our indoor mapping gives them clear, step-by-step directions to help them move from one location to another without getting confused. Whether they’re looking for a specific clinic, a departure gate, or a department office, they can easily find their destination on the map. Visitors no longer have to waste time asking for directions or wandering around. The system helps them stay on track, reducing stress and making their visit smoother.",
    image: "/images/guidance-in-complex-spaces.png",
  },
  {
    title: "2. One Clear Route for Every Journey",
    content:
      "Our system provides a direct route to meet the needs of all visitors. Whether someone is heading to a clinic, rushing to an airport gate, or simply visiting a store, we guide them along the fastest and most straightforward path. There are no complex options, just the quickest way to their destination, making every visit smooth and easy to follow.",
    image: "/images/one-clear-route-for-every-journey.png",
  },
  {
    title: "3. Access Across Multiple Devices",
    content:
      "People use different devices when navigating large venues. Whether they’re on their phone, tablet, or using a kiosk, beComap’s indoor mapping works across all of them. This means visitors can check a map on their phone while on the go or use a kiosk for more detailed directions when they arrive. It’s especially handy in places like malls or event centers where visitors might switch between devices based on what’s most convenient for them. Regardless of how they access it, they’ll get the same easy-to-use indoor map to guide them through the space.",
    image: "/images/access-across-multiple-devices.png",
  },
];

const WHY_DATA = [
  {
    title: "Access the Map",
    description:
      "Visitors can access the map directly via a web link, by scanning a QR code, or through features integrated with the mobile app's SDK.",
    iconUrl: "/images/icons/locations.png",
    activeIconUrl: "/images/icons/locations-active.png",
    color: "#34A853",
  },
  {
    title: "Enter Source",
    description:
      "Let visitors scan a QR code to access a clear, digital map that directs them step-by-step through the venue, helping them reach their destination without any detours.",
    iconUrl: "/images/icons/search-map.png",
    activeIconUrl: "/images/icons/search-map-active.png",
    color: "#34A853",
  },
  {
    title: "Enter the Destination",
    description:
      "Visitors can search or select their destination, like a specific store, room, or service area, from the map.",
    iconUrl: "/images/icons/type.png",
    activeIconUrl: "/images/icons/type-active.png",
    color: "#34A853",
  },
  {
    title: "Navigate Through",
    description:
      "The map provides step-by-step directions, guiding users through the venue quickly and without confusion.",
    iconUrl: "/images/icons/navigate-map.png",
    activeIconUrl: "/images/icons/navigate-map-active.png",
    color: "#34A853",
  },
  {
    title: "Reach Destination",
    description:
      "Arrive at the desired spot without getting lost. The navigation leads users straight to their desired location.",
    iconUrl: "/images/icons/destination.png",
    activeIconUrl: "/images/icons/destination-active.png",
    color: "#34A853",
  },
];

const IndoorNavigation = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <SolutionBanner
      source={"indoor-navigation"}
        bgImage={HeroImg}
        title="Reimagine Indoor Navigation with Our Indoor Mapping "
        description="Visitors shouldn't feel lost in your space. With beComap's Indoor Mapping, complex buildings become easy to navigate - whether it's a mall, airport, hospital, or event center."
      />
      <IndustryClients />
      <SolutionWhyUs
        title={"What Is Indoor Navigation?"}
        description={
          "Indoor navigation helps people move through complex spaces by providing clear directions and showing exactly how to reach their destination. Instead of feeling lost in complex buildings, visitors can follow clear directions to reach their destination. Whether they're searching for a specific store in a mall or a gate at an airport, indoor navigation helps make their journey straightforward and stress-free."
        }
        bgImage={SolutionWhyUsBg}
        subImage={WhySubImage}
        whyData={WHY_DATA}
      />
      <IndustrySolutions
        image={SecImg}
        solutions={SOLUTIONS}
        title={"The Building <br/> Blocks That<br/>  Make Navigation Work"}
        description={{
          part1:
            "beComap’s Indoor navigation is built using advanced technology that sets new standards for navigating complex spaces. First, our mapping system produces detailed digital layouts, covering everything from rooms and corridors to key areas like restrooms and service desks. These maps offer visitors a clear",
          part2:
            "view of the venue, helping them move through large spaces confidently.We use sensors and positioning technology, such as Bluetooth beacons, Wi-Fi signals, and QR codes, to accurately track visitors’ locations within the building. This technology allows users to know exactly where they are at any time.",
          part3:
            "Lastly, our navigation interface, accessible through mobile apps, web apps, or kiosks—provides clear directions and useful information, such as nearby facilities or events. This system sets new standards for how people navigate indoor spaces, making it simple and effective for all users.",
        }}
      />

      {/* 
      <IndustrySecondSection
        image={SecImg4}
        routeType="shoppingmall"
        title={"No More Mazes. Get Shoppers Straight to the stores."}
        description="Lost customers and missed sales opportunities are things of the past.  beComap's Retail Wayfinding Solution is here to change that. Our system turns confusion into clarity, allowing your mall visitors to navigate your store with ease. They can quickly find what they’re looking for, explore the latest deals, and stay informed about any changes—all without frustration. With beComap, your customers enjoy a continuous shopping experience, and you benefit from happier clients and increased foot traffic and sales."
      />
      <IndustrySolutions
        imageProperty={{
          width: 441,
          height: 505,
          top: "-120px",
          bottom: "0",
          justifyContentParent: "end",
          justifyContent: "end",
          backgroundColor: "gradient",
        }}
        image={SecImg}
        solutions={SOLUTIONS}
        title={"Mall Experience like never before"}
        description="Lost customers and missed sales opportunities are things of the past.  beComap's Retail Wayfinding Solution is here to change that. Our system turns confusion into clarity, allowing your mall visitors to navigate your store with ease. They can quickly find what they’re looking for, explore the latest deals, and stay informed about any changes—all without frustration. With beComap, your customers enjoy a continuous shopping experience, and you benefit from happier clients and increased foot traffic and sales."
      />
      <IndustryExperience
        image={SecImg2}
        title="Mall Navigation Made Easy"
        description="Our solution makes navigating your mall simple and stress-free. It offers detailed info on essential services like restrooms, ATMs, and customer service desks through the app. Shoppers can use easy turn-by-turn directions to find stores and services quickly. By enhancing their overall experience and reducing frustration, more visitors will be encouraged to spend time in our mall, increasing foot traffic and driving sales."
      />
      <IndustryAccordion
        title={"Discover 3X More Stores!"}
        bgImage={BgImg}
        description={
          "With beComap, shoppers get a complete list of all the stores in your mall, including detailed information and current offers. Now, they can explore more shops than ever before and find the best deals, turning every visit into an exciting shopping adventure and driving more foot traffic to your mall."
        }
        accordionData={accordionData}
        videoSrc={"/videos/mall.mp4"}
      />
    
      <IndustryHelp
        title="Why World’s largest venues trust beComap"
        description="beComap changes the way you think about retail navigation & engagement. Our platform simplifies navigation, attracts and retains customers, and optimizes operations to improve the overall shopping journey."
        image={SecImg3}
        points={[
          "Best-in-class indoor mapping for malls",
          "8 years on active R&D in location intelligence",
          "Optimized for use on mobile devices, web browsers, and kiosks",
          "Easy integration with existing apps",
          "Web-based navigation without any downloads",
          "Multiple language support",
        ]}
      />
 */}

      <SolutionsServices
      isIndoor={true}
        title="How We Set Up Your Kiosk for Indoor Navigation"
        description="Setting up your kiosk for indoor navigation includes several key steps to ensure a smooth experience for visitors. From choosing the best location for the kiosk to developing the map interface, we handle everything to make it easy for users to navigate your indoor space."
        image={"/images/kiosk-dummy.png"}
        serviceData={ServiceData}
      />
      <section
        className="section primary-bg"
        style={{
          backgroundImage: `url("/images/solution-bg.png")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          minHeight: "680px",
        }}
      >
        <Container>
          <Row className="align-items-center">
            <Col lg={7}>
              <Image
                src={"/images/solution-sec.png"}
                alt="indoor mapping software"
                width={550}
                height={450}
                style={{ width: "100%", height: "auto" }}
              />
            </Col>
            <Col lg={5}>
              <div className="section-head">
                <span
                  className="mb-4"
                  style={{
                    fontWeight: 600,
                    fontSize: "18px",
                  }}
                >
                  Case Study
                </span>
                <h2 className="main-title mb-3">Indoor mapping software</h2>
                <p>
                  Develop an ultra-modern travel environment that ree
                  experiences. Set new standards in travel convenience and
                  satisfaction through innovation, sustainability, and
                  operational efficiency.
                </p>
              </div>
              <button className="btn btn-dark mt-4">Learn More</button>
            </Col>
          </Row>
        </Container>
      </section>
      {/* <IndustryEBook
        image={"/images/mall-ebook-bg.png"}
        title={"Retail In Action"}
        description={`<p>According to ACI, global passenger traffic will reach 9.7 billion this year.</p> <p>Learn how to deliver a smooth travel experience with indoor maps in our helpful retail guide.</p>`}
      /> */}
      <Blogs />
      <TalkToOurTeam source={"indoor-navigation"} />
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={mallNavigationFAQ}
      />
    </div>
  );
};

export default IndoorNavigation;
