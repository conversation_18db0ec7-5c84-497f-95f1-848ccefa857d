.service-item-box {
  background: #ffffff;
  padding: 59px 50px 0;
  border-radius: 25px;
  margin-bottom: 30px;
  box-shadow: 0px 5px 80px 0px #dce1ea;
  display: flex;
  justify-content: space-between;
  flex-direction: column;

  &.sm {
    min-height: 490px;
    @media (max-width: 1199px) {
      min-height: 430px;
    }
  }
  @media (max-width: 1199px) {
    padding: 30px 30px 0;
  }
  position: relative;
  .srv-icon-bg {
    display: flex;
    height: 60px;
    width: 60px;
    background: $secondary-color;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-bottom: 23px;
  }
  .service-item-box-topsec {
    p {
      // min-height: 200px;
    }
  }
}
.box-titles {
  line-height: 50px;
  font-size: 24px;
  font-family: $font-bold;
  margin-bottom: 7px;
}

.service-item-split-box {
  .service-item-box-topsec {
    width: 44%;
  }
  .service-item-box-footer {
    width: 50%;
  }
}
.img-leftalign-footer-small,
.img-rightalign-footer-small {
  .service-itembox-img-wrap {
    margin-right: -50px;
    margin-left: -50px;
    &.sec {
      margin-right: -50px;
      margin-left: -30px;
    }
    @media (max-width: 1199px) {
      margin-right: -30px;
      margin-left: -30px;
      &.sec {
        margin-right: -30px;
        margin-left: -30px;
      }
    }
  }
}
.service-itembox-img-wrap {
  &.sec {
    margin-right: -50px;
    margin-left: -30px;
    @media (max-width: 1199px) {
      margin-right: -30px;
      margin-left: -30px;
    }
  }
}
.service-three-box {
  .service-item-box-topsec {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 20px;
    .srv-icon-bg,
    .box-titles {
      margin-bottom: 0;
    }
    .box-titles {
      line-height: 26px;
    }
  }
  // .service-itembox-img-wrap {
  //     img {
  //         height: 247px !important;
  //     }
  // }
}
.service-item-box:hover {
  .srv-overlay {
    opacity: 1;
    visibility: visible;
    transition-delay: 0s;
  }
}
.srv-overlay {
  opacity: 0;
  visibility: hidden;
  transition: visibility 0s linear 0.3s, opacity 0.3s linear;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #000;
  background-image: url("/images/overlay-bg.png");
  background-repeat: no-repeat;
  background-position: center;
  right: 0;
  bottom: 0;
  z-index: 4;
  border-radius: 30px;
  padding: 20px 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #fff;
  .btn {
    min-width: 196px;
  }
  .srv-icon-bg {
    background-color: #fff !important;
  }
  p {
    margin-bottom: 40px;
  }
}
@media (max-width: 1400px) {
  img.service-box-img {
    max-width: 100%;
    height: auto !important;
  }
}
@media (max-width: 767px) {
  .service-item-box {
    flex-direction: column;
  }
  .service-item-split-box {
    .service-item-box-topsec {
      width: 100%;
    }
    .service-item-box-footer {
      width: 100%;
    }
  }
}
