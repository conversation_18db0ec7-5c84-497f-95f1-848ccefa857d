.talktoteam-section {
    background-color: $primary-color;
    background-image: url("/images/talkto-team-bg.png");
    background-position: 0 0;
    background-repeat: no-repeat;
    .section-head {
        color: #fff;
    }
}
.react-tel-input .form-control {
    padding-left: 60px!important;
}
.talkto-form-wrap {
    input.form-control {
        border-radius: 50px;
        height: 60px;
        padding: 0 20px;
        font-size: 14px;
        color: #000;
        &::placeholder {
            color: #494651;
        }
        &:not(.is-invalid) {
            border: 1px solid #d9cccc;

        }

        &:hover , &:focus {
            border: 2px solid $primary-color;
            box-shadow: none;
        }
        
    }
    .form-control {
        &:hover , &:focus {
            border: 2px solid $primary-color;
            box-shadow: none;
        }
    }


    .form-select {
        border: 1px solid rgba(230, 230, 230, 0.53);
        border-radius: 100px;
        height: 60px;
        background-color: transparent;
        color: #fff;
        font-size: 14px;
        font-family: $font-medium;
        background-image: none;
        padding: 0 20px;
        cursor: pointer;
        option {
            background-color: #fff; /* Background color of options */
            color: #000; /* Text color of options */
        }
        &:hover , &:focus {
            border: 2px solid $primary-color;
            box-shadow: none;
        }
    }
}
.talkto-form-item {
    margin-bottom: 30px;
}
.select-arrow {
    position: absolute;
    right: 2px;
    top: 2px;
    width: 56px;
    height: 56px;
    // background: #f00;
    text-align: center;
    align-items: center;
    display: flex;
    justify-content: center;
    border-radius: 100px;
    pointer-events: none;
}
.talktoteam-left {
    img {
        max-width: 100% !important;
        height: auto !important;
    }
}
@media (max-width: 767px) {
    .talktoteam-left {
        img {
            max-width: 60% !important;
            margin: 0 auto 30px;
            display: block;
        }
    }
}
