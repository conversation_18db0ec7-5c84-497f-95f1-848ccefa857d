"use client";
import { Container, <PERSON>, <PERSON> } from 'react-bootstrap';
import ContactDetails from './_components/contactdetails';
import ContactForm from './_components/contactform';
import dynamic from 'next/dynamic';
// import OfficeAddress from './_components/offices';

// export const metadata = {
//     metadataBase: new URL('https://becomap.com'),
//     title: 'Contact Us | beComap',
//     description: "Need to get in touch with beComa<PERSON>? Contact our team for inquiries, feedback, or support. We're here to assist you. Reach out to us today",
//     images: ["/becomap.png"],
//     openGraph: {
//       title: 'Contact Us | beComap',
//     description: "Need to get in touch with be<PERSON><PERSON><PERSON>? Contact our team for inquiries, feedback, or support. We're here to assist you. Reach out to us today",
//       images: ["/becomap.png"],
//     },
//     alternates: {
//       canonical: '/contact ',
//     }
//   } 


  const DynamicOfficeAddress = dynamic(
    () => import('./_components/offices'), // Adjust the import path as needed
    { ssr: false }
  );

const Contact = () => {
    return (
        <div className='contact-wrap'>
            <Container className="contact-container mb-5" fluid={"xl"}>
                <Row className="contact-row d-flex">
                    <Col xl={6} className='contact-left-col '>
                        <ContactDetails />
                    </Col>
                    <Col xl={6} className='contact-right-col'>
                        <ContactForm />
                    </Col> 
                </Row>
            </Container>
            <DynamicOfficeAddress/>
        </div>
    );
};

export default Contact;
