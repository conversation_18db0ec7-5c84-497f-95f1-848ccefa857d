import React from "react";
import { Container, <PERSON>, <PERSON> } from "react-bootstrap";
import Testimonialslider from "./_components/testimonialslider";
import Image from "next/image";

const ClientFeedback = () => {
  const points = [
    "Quick and easy deployment",
    "Technology and Hardware: Universal tracking solution",
    "Precision: up to 0.1 feet accuracy of localization",
    "Solution proved over time: 10 year on the market with over 3000 buildings covered",
  ];
  return (
    <div className="section client-feedback-section p-relative">
      <Container>
        <Row className="client-ffedback-wrap align-items-center">
          <Col lg={6} className="client-feedback-left">
            <Testimonialslider />
          </Col>
          <Col lg={6} className="client-feedback-right flex-1">
            <div className="section-head">
              <h2 className="main-title mb-4">
                Our clients knows the value we provide
              </h2>
              <p>
                At beComap, we provide powerful indoor navigation software and
                related location services that help businesses to enhance their
                customer experiences and streamline operations within vast and
                complex indoor environments.{" "}
              </p>
            </div>
            <div className="key-points-wrap">
              <h4>Our Advantages</h4>
              <ul>
                {points.map((point, index) => (
                  <li key={`point-${index}`}>
                    <span className="list-tick-img">
                      <svg
                        width="31"
                        height="31"
                        viewBox="0 0 31 31"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        
                        <circle cx="16" cy="16" r="16" fill="#FFB82D" />
                        <circle
                          cx="16"
                          cy="16"
                          r="14.5"
                          stroke="white"
                          strokeOpacity="0.88"
                          strokeWidth="3"
                        />
                        <path
                          d="M13.4587 22.2084L23.7399 11.9272L21.6982 9.8855L13.4587 18.1251L9.30241 13.9688L7.26074 16.0105L13.4587 22.2084ZM15.5003 30.0834C13.483 30.0834 11.5871 29.7006 9.81283 28.935C8.03852 28.1694 6.49512 27.1303 5.18262 25.8178C3.87012 24.5053 2.83105 22.9619 2.06543 21.1876C1.2998 19.4133 0.916992 17.5174 0.916992 15.5001C0.916992 13.4827 1.2998 11.5869 2.06543 9.81258C2.83105 8.03828 3.87012 6.49487 5.18262 5.18237C6.49512 3.86987 8.03852 2.83081 9.81283 2.06519C11.5871 1.29956 13.483 0.916748 15.5003 0.916748C17.5177 0.916748 19.4135 1.29956 21.1878 2.06519C22.9621 2.83081 24.5055 3.86987 25.818 5.18237C27.1305 6.49487 28.1696 8.03828 28.9352 9.81258C29.7008 11.5869 30.0837 13.4827 30.0837 15.5001C30.0837 17.5174 29.7008 19.4133 28.9352 21.1876C28.1696 22.9619 27.1305 24.5053 25.818 25.8178C24.5055 27.1303 22.9621 28.1694 21.1878 28.935C19.4135 29.7006 17.5177 30.0834 15.5003 30.0834ZM15.5003 27.1667C18.7573 27.1667 21.516 26.0365 23.7764 23.7761C26.0368 21.5157 27.167 18.757 27.167 15.5001C27.167 12.2431 26.0368 9.48446 23.7764 7.22404C21.516 4.96362 18.7573 3.83342 15.5003 3.83342C12.2434 3.83342 9.4847 4.96362 7.22428 7.22404C4.96387 9.48446 3.83366 12.2431 3.83366 15.5001C3.83366 18.757 4.96387 21.5157 7.22428 23.7761C9.4847 26.0365 12.2434 27.1667 15.5003 27.1667Z"
                          fill="#ffff"
                        />
                      </svg>
                    </span>
                    {point}
                  </li>
                ))}
              </ul>
            </div>
          </Col>
        </Row>
      </Container>
      <div className="testimonialsec-bg-right-bottom">
        <Image
          src="/images/testimonial-bg-bottom.png"
          alt="testimonial-section-bg"
          width={306}
          height={331}
        />
      </div>
    </div>
  );
};

export default ClientFeedback;
