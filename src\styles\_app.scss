@font-face {
  font-family: "ObjectivityRegular";
  src: url("../assets/fonts/regular/ObjectivityRegular.eot");
  src: url("../assets/fonts/regular/ObjectivityRegular.eot")
      format("embedded-opentype"),
    url("../assets/fonts/regular/ObjectivityRegular.woff2") format("woff2"),
    url("../assets/fonts/regular/ObjectivityRegular.woff") format("woff"),
    url("../assets/fonts/regular/ObjectivityRegular.ttf") format("truetype");
}
@font-face {
  font-family: "ObjectivityExtraBold";
  src: url("../assets/fonts/extrabold/ObjectivityExtraBold.eot");
  src: url("../assets/fonts/extrabold/ObjectivityExtraBold.eot")
      format("embedded-opentype"),
    url("../assets/fonts/extrabold/ObjectivityExtraBold.woff2")
      format("woff2"),
    url("../assets/fonts/extrabold/ObjectivityExtraBold.woff") format("woff"),
    url("../assets/fonts/extrabold/ObjectivityExtraBold.ttf")
      format("truetype");
}
@font-face {
  font-family: "ObjectivityMedium";
  src: url("../assets/fonts/medium/ObjectivityMedium.eot");
  src: url("../assets/fonts/medium/ObjectivityMedium.eot")
      format("embedded-opentype"),
    url("../assets/fonts/medium/ObjectivityMedium.woff2") format("woff2"),
    url("../assets/fonts/medium/ObjectivityMedium.woff") format("woff"),
    url("../assets/fonts/medium/ObjectivityMedium.ttf") format("truetype");
}
@font-face {
  font-family: "ObjectivityBold";
  src: url("../assets/fonts/bold/ObjectivityBold.eot");
  src: url("../assets/fonts/bold/ObjectivityBold.eot")
      format("embedded-opentype"),
    url("../assets/fonts/bold/ObjectivityBold.woff2") format("woff2"),
    url("../assets/fonts/bold/ObjectivityBold.woff") format("woff"),
    url("../assets/fonts/bold/ObjectivityBold.ttf") format("truetype");
}

body {
  font-family: $font;
  color: $body-color;
  font-size: $body-font-size;
  line-height: 30px;
}
.flex-1 {
  flex: 1;
}
.main-title {
  font-family: $font-bold;
  font-size: 40px;
  line-height: 50px;
}
.section {
  padding: 110px 0;
}
.btn {
  background: #ffffff;
  box-shadow: 0px 10px 30px rgba(69, 69, 69, 0.09);
  border-radius: 100px;
  min-width: 265px;
  color: #000000;
  border: none;
  height: 50px;
  font-family: $font-medium;
  &.btn-secondary {
    background-color: $secondary-color;
    font-family: $font-medium;
    font-size: 15px;
    border: 2.5px solid $secondary-color;

  }
  &:hover,
  &:active,
  &:focus {
    opacity: .8;
  }
  &.btn-dark {
    background-color: black;
    color: #fff;
  }
  &.btn-outline {
    border: 2.5px solid $secondary-color;
    color: $body-color;
  }


}


.border-btn {
  border: 1px solid $primary-color;
  background-color: $primary-color;
  border-radius: 100px;
  color: #fff;
  font-size: 16px;
  font-family: $font-medium;
  text-decoration: none;
  &.medium-btn {
    height: 36px;
    font-size: 14px;
    font-family: $font-medium;
    min-width: 100px;
    padding: 0 17px;
    line-height: 36px;
  }
  &.large-btn {
    min-width: 152px;
  }
  a {
    text-decoration: none;
    color: #000;
  }
  &:hover,
  &:active,
  &:focus {
    opacity: .8;
  }
}
.mb-60 {
  margin-bottom: 60px;
}
.secondary-bg {
  background-color: $primary-color;
}
.primary-bg {
  background-color: $secondary-color;
}
.secondary-color {
  color: $primary-color;
}
.primary-color {
  color: $secondary-color;
}
.p-relative {
    position: relative;
}
.mb-30 {
  margin-bottom: 30px;
}
.mb-40 {
  margin-bottom: 40px;
}
.mb-24 {
  margin-bottom: 24px;
}
.mt-24 {
  margin-top: 24px;
}
.no-wrap {
  flex-wrap: nowrap;
}
.double-btn {
  display: flex;
  .btn {
    margin-left: 20px;
    &:first-child {
      margin-left: 0;
    }
  }
}
.size-24 {
  font-size: 24px;
}
.size-20 {
  font-size: 20px;
}
.bold {
  font-family: $font-bold;
}
@media (max-width: 768px) {
  .section {
    padding: 30px 0;
  }
  .main-title {
    font-size: 24px;
    line-height: 30px;
  }
}

.bg-bc-light {
  background: #f3f3f3;
}
