@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(5deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

.amz-people-img-wrap {
    background-image: url("/images/amz-people-bg.png");
    height: 249px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 12px;
    transition: transform 0.5s ease-in-out;
    &:hover {
        animation: rotate 0.5s ease-in-out;
    }
    figure {
        margin: 0;
        width: 224px;
        height: 224px;
    }
    img {
        width: 100% !important;
        height: auto !important;
        border-radius: 50%;
    }
}
.amz-people-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    width: 249px;
    padding-top: 80px;
}
.amz-people-names {
    h4 {
        margin-bottom: 0;
    }
    p {
        font-size: 16px;
        font-family: $font-medium;
    }
}
