// API Constants
export const API_BASE_URL = 'https://blog-api.becomap.com';

// API Endpoints
export const API_ENDPOINTS = {
  POSTS: '/posts/',
  CONTACT: '/contact-beco/',
  DOWNLOADS: '/beco-downloadscontacts/',
  SUBSCRIBE: '/subscribe-beco/',
};

// Headers
export const API_HEADERS = {
  BLOG_IDENTIFIER: 'beco',
  CONTENT_TYPE: 'application/json',
};

// Site Configuration
export const SITE_CONFIG = {
  name: 'becomap',
  url: 'https://becomap.com',
  logo: 'https://becomap.com/images/logo.png',
  description: 'Indoor navigation system to improve visitor experience and staff management with indoor mapping & indoor wayfinding.',
  image: '/becomap.png',
};

// Contact Information
export const CONTACT_INFO = {
  phones: ['+91 9778402640', '+91 9778402646', '+91 9778414320'],
  address: {
    street: 'CWS#12, Room No. 17, Second Floor, Geo Infopark',
    city: 'Kakkanad,Kochi',
    country: 'India',
    postalCode: '682042',
  },
  socialMedia: {
    facebook: 'https://www.facebook.com/becoMap//',
    twitter: 'https://twitter.com/becomap1',
    instagram: 'https://www.instagram.com/becomap_/',
    linkedin: 'https://www.linkedin.com/company/beco-technologies-pvt-ltd/',
  },
};
