.soultion-wrapper {
  border-radius: 30px;
  margin-bottom: 50px;
  margin-top: 40px;
  .main-title {
    margin-bottom: 20px !important;
    @media (max-width: 767px) {
    }
    @media (max-width: 1399px) {
      font-size: 36px;
      line-height: 42px;
      margin-bottom: 8px !important;
    }
    @media (max-width: 1199px) {
      font-size: 30px;
      line-height: 36px;
    }
    @media (max-width: 991px) {
      font-size: 26px;
      line-height: 30px;
    }
  }
  p {
    margin-bottom: 0 !important;
    line-height: 26px;
    @media (max-width: 767px) {
      margin: 0 auto;
    }
  }
  .soultion-details {
    padding: 50px 0 50px 70px;

    @media (max-width: 767px) {
      padding: 40px;
    }

    p {
      line-height: 26px;
    }
  }
}

.solution-item-box {
  box-shadow: 0px 20px 50px 0px #7c7c7c26;
  background: #ffffff;
  padding: 30px;
  border-radius: 30px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: stretch;
  height: 100%;

  @media (max-width: 1399px) {
    padding: 25px 30px 15px;
    p {
      line-height: 28px;
    }
  }
  @media (max-width: 1199px) {
    padding: 20px;
  }

  .srv-icon-bg {
    width: 54px;
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-bottom: 18px;
  }
  .small-head {
    margin-bottom: 0;
    font-family: $font-bold;
    font-size: 26px;
    flex: 1;
    margin-bottom: 15px;
    @media (max-width: 1399px) {
      font-size: 24px;
      line-height: 28px;
    }
    @media (max-width: 1199px) {
      font-size: 22px;
      line-height: 26px;
      margin-bottom: 15px;
    }
  }
}

.sl-img-wrap {
  position: relative;
  margin-top: -120px;
}

.ex-section {
  padding: 80px 0;
  @media (max-width: 1199px) {
    .main-title {
      font-size: 38px;
      line-height: 39px;
    }
  }
  @media (max-width: 991px) {
    .main-title {
      font-size: 34px;
      line-height: 36px;
    }
  }
}


.sl-video {
  
  #sl-video {
    box-shadow: 0px 5px 20px 0px #00000014;
    padding: 15px;
    background-color: #fff;
    border-radius: 12px;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: auto;
    height: 100%;
    margin: 0 auto;
  }
}