@import "./utils/variables";

// .......Common Style.......
@import "./app";

// .......Header....../
@import "./components/header/header";

// .......Footer....../
@import "./components/footer/footer";
// .......Components....../
@import "./components/talktoourteam/talktoourteam";

// .......Home....../
@import "./pages/home/<USER>/banner";
@import "./pages/home/<USER>/clients";
@import "./pages/home/<USER>/mappingpartner";
@import "./pages/home/<USER>/technologyweuse";
@import "./pages/home/<USER>/serviceshelpsyou";
@import "./pages/home/<USER>/clientfeedback";
@import "./pages/home/<USER>/components/testimonialslider";
@import "./pages/home/<USER>/blogs";
@import "./pages/home/<USER>/ourservice";

// .......Blog....../
@import "./pages/blog/blog";
@import "./pages/blog/components/blogpost/blogpost";
@import "./pages/blog/components/search/search";
@import "./pages/blog/components/subscribe/blogsubscribe";
@import "./pages/blog/components/ebook/ebook";

// .......BlogDetail....../
@import "./pages/blogdetail/blogdetail";
@import "./pages/blogdetail/components/blogdetailcontent/blogdetailcontent";
@import "./pages/blogdetail/components/blogdetailsleft/blogdetailsleft";

// .......Contact....../
@import "./pages/contact/contact";
@import "./pages/contact/components/contactdetails/contactdetails";
@import "./pages/contact/components/contactform/contactform";
@import "./pages/contact/components/offices/offices";

// .......Company....../
@import "./pages/company/company";
@import "./pages/company/components/achievements/achievements";
@import "./pages/company/components/clientsay/clientsay";
@import "./pages/company/components/companybanner/companybanner";
@import "./pages/company/components/magicrecipe/magicrecipe";
@import "./pages/company/components/mission/mission";
@import "./pages/company/components/ourvalues/ourvalues";
@import "./pages/company/components/peoplebehind/peoplebehind";

// .......Career....../
@import "./pages/career/career";
@import "./pages/career/components/careerbanner/careerbanner";
@import "./pages/career/components/culture/culture";
@import "./pages/career/components/corevalue/corevalue";
@import "./pages/career/components/positions/positions";
@import "./pages/career/components/positions/components/positioncontent/positioncontent";

// .......Casestudy....../
@import "./pages/casestudy/casestudy";
@import "./pages/casestudy/components/casestudybanner/casestudybanner";
@import "./pages/casestudy/components/casestudyitem/casestudyitem";


// .......Industry....../

@import "./pages/industry/industry";

// .......Solutions....../
@import "./pages/solutions/solutions";

@import "./pages/thankyou/thankyou";


.angry-grid {
    display: grid; 
 
    grid-template-rows: 1fr 1fr 1fr 1fr;
    grid-template-columns: 1fr 1fr 1fr;
    
    gap: 0px;
    height: 100%;
    
 }
   
 #item-0 {
 
    background-color: #5569D6; 
    grid-row-start: 1;
    grid-column-start: 1;
 
    grid-row-end: 2;
    grid-column-end: 2;
    
 }
 #item-1 {
 
    background-color: #D8D69F; 
    grid-row-start: 1;
    grid-column-start: 2;
 
    grid-row-end: 2;
    grid-column-end: 3;
    
 }
 #item-2 {
 
    background-color: #CF5B5B; 
    grid-row-start: 1;
    grid-column-start: 3;
 
    grid-row-end: 3;
    grid-column-end: 4;
    
 }
 #item-3 {
 
    background-color: #77A5BD; 
    grid-row-start: 2;
    grid-column-start: 2;
 
    grid-row-end: 3;
    grid-column-end: 3;
    
 }
 #item-4 {
 
    background-color: #DFBD8C; 
    grid-row-start: 2;
    grid-column-start: 1;
 
    grid-row-end: 3;
    grid-column-end: 2;
    
 }
 #item-5 {
 
    background-color: #B788EB; 
    grid-row-start: 3;
    grid-column-start: 1;
 
    grid-row-end: 4;
    grid-column-end: 2;
    
 }
 #item-6 {
 
    background-color: #55C5BC; 
    grid-row-start: 3;
    grid-column-start: 2;
 
    grid-row-end: 4;
    grid-column-end: 3;
    
 }
 #item-7 {
 
    background-color: #5A7C5B; 
    grid-row-start: 3;
    grid-column-start: 3;
 
    grid-row-end: 4;
    grid-column-end: 4;
    
 }
 #item-8 {
 
    background-color: #77F6B9; 
    grid-row-start: 4;
    grid-column-start: 1;
 
    grid-row-end: 5;
    grid-column-end: 2;
    
 }
 #item-9 {
 
    background-color: #9896E5; 
    grid-row-start: 4;
    grid-column-start: 2;
 
    grid-row-end: 5;
    grid-column-end: 3;
    
 }
 #item-10 {
 
    background-color: #C6A9D8; 
    grid-row-start: 4;
    grid-column-start: 3;
 
    grid-row-end: 5;
    grid-column-end: 4;
    
 }
    