"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import Link from "next/link";
import Image from "next/image";
import Container from "react-bootstrap/Container";
import { Autoplay } from "swiper/modules";
import { useEffect, useState } from "react";

const ClientSlider = () => {
  const [listOfImages, setListOfImages] = useState([]);

  useEffect(() => {
    const importAll = (r) =>
      r.keys().map((filePath) => {
        const fileName = filePath.split("/").pop(); 
        const altText = fileName.replace(/\.[^/.]+$/, "").replace(/-/g, " ");
        return { src: r(filePath), alt: altText }; 
      });

    const images = importAll(
      require.context(
        "/public/images/clientimages/",
        false,
        /\.(png|jpe?g|svg)$/
      )
    );
    setListOfImages(images);
  }, []);

  
  
  
  
  return (
    <div className="cleientslider-wrap">
      <Container>
        <h5 className="sub-title">
          MILLIONS OF PEOPLE VISITING THE WORLD’S LARGEST VENUES USE BECOMAP
        </h5>
        <Swiper
          slidesPerView={6}
          spaceBetween={30}
          className="clientslider"
          loop={true}
          autoplay={{
            delay: 2500,
            disableOnInteraction: false,
          }}
          modules={[Autoplay]}
          breakpoints={{
            1200: {
              slidesPerView: 6,
            },
            992: {
              slidesPerView: 5,
            },
            768: {
              slidesPerView: 3,
            },
            0: {
              slidesPerView: 2,
            },
          }}
        >
          {listOfImages.map((item) => (
            <SwiperSlide key={item.id}>
              <div>
            
                  <Image
                    src={item.src}
                    alt={item.alt}
                    width="145"
                    height="81"
                  />
                 
              </div>
            </SwiperSlide>
          ))}
         
        </Swiper>
      </Container>
    </div>
  );
};

export default ClientSlider;
