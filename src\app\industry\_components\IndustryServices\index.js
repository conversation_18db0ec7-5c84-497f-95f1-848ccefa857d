"use client";
import Image from "next/image";
import { Container, Row, Col } from "react-bootstrap";

const ServiceContent = ({ data, isReversed }) => (
  <div
    className={`service-item-content ${
      isReversed ? "reverse-content text-start" : "text-start"
    }`}
  >
    <div
      className={`service-icon-wrap d-flex flex-column ${
        isReversed
          ? "align-items-lg-start align-items-center"
          : "align-items-lg-start align-items-center"
      }`}
    >
      <span className="srv-icon">
        <Image
          src={data.icon}
          alt={data.title}
          width={30}
          height={30}
          className="solution-icon-img"
        />
      </span>
      <h4 className="small-head">{data.title}</h4>
    </div>
    <p className="mb-0">{data.content}</p>
  </div>
);

const IndustryServices = ({ title, description, serviceData, extraData }) => {
  return (
    <section
      className={`section service-section overflow-hidden ${
        extraData ? "" : "pb-0"
      }`}
    >
      <Container fluid style={{ padding: "0px" }}>
        <div className="company-value-head">
          <h2 className="main-title">{title}</h2>
          <p>{description}</p>
        </div>
        {serviceData.map((service, index) => {
          const isReversed = index % 2 !== 0;
          return (
            <Row
              key={index}
              className={`align-items-center g-0 position-relative ser-cr ${
                isReversed ? "justify-content-start" : "justify-content-end"
              }`}
              style={{ backgroundColor: isReversed ? "#f3f3f3" : "#ffffff" }}
            >
              {isReversed ? (
                <>
                  <Col lg={6}>
                    <div className="im-wrapper reverse-img">
                      <Image
                        src={service.image}
                        alt={service.title}
                        width="452"
                        height="368"
                        style={{ width: "100%", height: "auto" }}
                      />
                    </div>
                  </Col>
                  <Col xxl={5} lg={6}>
                    <ServiceContent data={service} isReversed={isReversed} />
                  </Col>
                </>
              ) : (
                <>
                  <Col xxl={5} lg={6} className="order-2 order-lg-1">
                    <ServiceContent data={service} isReversed={isReversed} />
                  </Col>
                  <Col lg={6} className="order-1 order-lg-2">
                    <div className="im-wrapper">
                      <Image
                        src={service.image}
                        alt={service.title}
                        width="452"
                        height="368"
                        style={{ width: "100%", height: "auto" }}
                      />
                    </div>
                  </Col>
                </>
              )}
            </Row>
          );
        })}
      </Container>
      {extraData && (
        <section className="section service-extra-section overflow-hidden pb-0">
          <Container fluid>
            {extraData.map((data, index) => (
              <Row
                key={index}
                className={`align-items-center ${
                  index % 2 !== 0 ? "mt-5 justify-content-end" : ""
                }`}
              >
                {index % 2 !== 0 ? (
                  <>
                    <Col xxl={5} lg={6} md={8} className="order-2 order-lg-1">
                      <div className="service-item-content text-start">
                        <div className="service-icon-wrap d-flex flex-column align-items-start">
                          <h4
                            className="small-head"
                            dangerouslySetInnerHTML={{ __html: data.title }}
                          />
                        </div>
                        <p className="mb-0">{data.description}</p>
                      </div>
                    </Col>
                    <Col lg={6} md={8} className="order-1 order-lg-2">
                      <Image
                        src={data.image}
                        alt={data.title}
                        width="452"
                        height="368"
                        style={{ width: "100%", height: "auto" }}
                      />
                    </Col>
                  </>
                ) : (
                  <>
                    <Col lg={6} md={8}>
                      <Image
                        src={data.image}
                        alt={data.title}
                        width="452"
                        height="368"
                        style={{ width: "100%", height: "auto" }}
                      />
                    </Col>
                    <Col xxl={5} lg={6} md={8} >
                      <div className="service-item-content text-start">
                        <div className="service-icon-wrap d-flex flex-column align-items-start">
                          <h4
                            className="small-head"
                            dangerouslySetInnerHTML={{ __html: data.title }}
                          />
                        </div>
                        <p className="mb-0">{data.description}</p>
                      </div>
                    </Col>
                  </>
                )}
              </Row>
            ))}
          </Container>
        </section>
      )}
    </section>
  );
};

export default IndustryServices;
