import { isValidPhoneNumber } from "libphonenumber-js";

export const validatePhoneNumber = (_, value) => {
  if (!value) {
    return Promise.resolve();
    // return Promise.reject(new Error("Please enter a valid phone number"));
  }
  // Use libphonenumber-js to check for a valid phone number
  if (isValidPhoneNumber(`+${value}`)) {
    return Promise.resolve();
  } else {
    return Promise.reject(new Error("Invalid phone number format"));
  }
};
