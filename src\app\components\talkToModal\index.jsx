"use client";
import { useState } from "react";
import { But<PERSON>, Form, Row, Col, Modal } from "react-bootstrap";
import Image from "next/image";
import { formSubmit } from "@/app/action/action";
import { Formik } from "formik";
import { useRouter } from "next/navigation";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/bootstrap.css";

const TalkToModal = ({ isOpen, onClose, hitUrl }) => {
  const router = useRouter();
  const [formStatus, setFormStatus] = useState(false);
  const [phone, setPhone] = useState("");

  return (
    <Modal show={isOpen} centered size="xl" onHide={onClose} className="get-demo-popup" backdrop="static">
      <Modal.Body>
        <button className="close-btn" onClick={onClose} type="button">
          <Image
            src="/images/icons/close.png"
            alt="close-icon"
            width={30}
            height={30}
          />
        </button>
        <Row className="align-items-center">
          <Col lg={5}>
            <div className="get-demo-popup-content">
              <h3>Ready to unlock the power of location?</h3>
              <Image
              className="d-none d-lg-block"
                src="/images/3dman.png"
                alt="contact-image"
                width="400"
                height="400"
                style={{ width: "100%", height: "auto" }}
              />
            </div>
          </Col>
          <Col lg={7}>
            <div className="talkto-form-wrap">
              <Formik
                initialValues={{
                  name: "",
                  email: "",
                  phone_number: "",
                  option: "",
                  message: "",
                }}
                validate={(values) => {
                  const errors = {};
                  
                  if (!values.name) errors.name = "Name is required";

                  if (!values.email) {
                    errors.email = "Email is required";
                  } else if (
                    !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)
                  ) {
                    errors.email = "Invalid email format";
                  }

                  if (!values.phone_number || values.phone_number.length < 10) {
                    errors.phone_number = "Valid phone number is required";
                  }

                  if (!values.option) errors.option = "Please select an option";

                  return errors;
                }}
                onSubmit={async (values, { setSubmitting, resetForm }) => {
                  try {
                    const status = await formSubmit(values, `modal-${hitUrl}`);
                    if (status === 201) {
                      setSubmitting(true);
                      router.push("/thankyou");
                      setFormStatus(true); 
                      setTimeout(() => {
                        setFormStatus(false);
                        setSubmitting(false);
                        resetForm();
                      }, 5000);
                    } else {
                      console.error(
                        `Form submission failed with status: ${status}`
                      );
                    }
                  } catch (error) {
                    console.error(`Form submission failed: ${error.message}`);
                  }
                }}
              >
                {({
                  values,
                  errors,
                  touched,
                  handleChange,
                  handleBlur,
                  handleSubmit,
                  isSubmitting,
                  setFieldValue,
                }) => (
                  <Form onSubmit={handleSubmit}>
                    <Row className="talkto-form-row">
                      <Col md={12}>
                        <Form.Group className="talkto-form-item">
                          <Form.Label>Name*</Form.Label>
                          <Form.Control
                            type="text"
                            name="name"
                            placeholder="Name"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.name}
                            isInvalid={errors.name && touched.name}
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.name}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Col>
                      <Col md={12}>
                        <Form.Group className="talkto-form-item">
                          <Form.Label>Email*</Form.Label>

                          <Form.Control
                            type="text"
                            name="email"
                            placeholder="Email Address*"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.email}
                            isInvalid={errors.email && touched.email}
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.email}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Col>

                      <Col md={12}>
                        <Form.Group className="talkto-form-item">
                          <PhoneInput
                            inputProps={{
                              name: "phone_number",
                              onBlur: handleBlur,
                              className: "form-control w-100",
                            }}
                            placeholder="Phone Number*"
                            country={"us"}
                            enableSearch
                            onChange={(phone) =>
                              setFieldValue("phone_number", phone)
                            }
                            value={phone}
                          />
                          {errors.phone_number && touched.phone_number && (
                            <div className="invalid-feedback d-block">
                              {errors.phone_number}
                            </div>
                          )}
                        </Form.Group>
                      </Col>

                      <Col md={12}>
                        <Form.Group className="talkto-form-item p-relative">
                          <Form.Label>Your inquiry*</Form.Label>

                          <Form.Select
                            name="option"
                            value={values.option}
                            onChange={(e) =>
                              setFieldValue("option", e.target.value)
                            }
                            onBlur={handleBlur}
                            isInvalid={touched.option && !!errors.option}
                          >
                            <option
                              value={""}
                              key="blankChoice"
                              hidden
                              disabled
                            >
                              Your inquiry*
                            </option>
                            <option value="Indoor navigation">
                              Indoor navigation
                            </option>
                            <option value="Asset tracking">
                              Asset tracking
                            </option>
                            <option value="General Inquiry">
                              General Inquiry
                            </option>
                          </Form.Select>
                          <Form.Control.Feedback type="invalid">
                            {errors.option}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Col>

                      <Col md={12}>
                        <Button
                        style={{ minWidth:"auto" }}
                          variant="secondary"
                          type="submit"
                          className={`w-100 form-btn ${
                            isSubmitting ? "loading" : ""
                          }`}
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <div class="loader">
                              <div class="dot-elastic"></div>
                            </div>
                          ) : formStatus ? (
                            <span>Submitted</span>
                          ) : (
                            <span>Talk to our Team</span>
                          )}
                        </Button>
                      </Col>
                    </Row>
                  </Form>
                )}
              </Formik>
            </div>
          </Col>
        </Row>
      </Modal.Body>
    </Modal>
  );
};

export default TalkToModal;
