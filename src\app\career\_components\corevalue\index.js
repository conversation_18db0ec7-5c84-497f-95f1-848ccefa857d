import React from 'react';
import Image from 'next/image';

import { But<PERSON>, Container } from 'react-bootstrap';

const CoreValue = () => {
    return (
        <section className='section core-value-wrap'>
            <Container>
                  <div className='d-flex corevalue-row align-items-center'>
                    <div className='corevalue-left'>
                        <div className='corevalue-img-box corevalue-img-box-1'>
                            <div className='core-value-img-text core-value-img-text-left'>
                                <span>A positive attitude</span>
                            </div>
                            <div className='corevalue-img-wrap corevalue-imgwrap-gray'>
                                <Image src='/images/attittude.png' alt='career' width="178" height="148" />
                            </div>
                        </div>
                        <div className='corevalue-img-box corevalue-img-box-2'>
                            <div className='core-value-img-text core-value-img-text-right'>
                                <span>A Philonoist by choice</span>
                            </div>
                            <div className='corevalue-img-wrap corevalue-imgwrap-black'>
                                <Image src='/images/choice.png' alt='career' width="153" height="117" />
                            </div>
                        </div>
                        <div className='corevalue-img-box corevalue-img-box-3'>
                            <div className='core-value-img-text core-value-img-text-left'>
                                <span>Desire to be exceptional</span>
                            </div>
                            <div className='corevalue-img-wrap corevalue-imgwrap-secondary-color'>
                                <Image src='/images/exceptional.png' alt='career' width="182" height="78"  />
                            </div>
                        </div>
                    </div>
                    <div className='corevalue-right'>
                        <h2 className='main-title mb-30'>Our core values set the tone for who we are</h2>
                        <p className='mb-30'>Our singular mission is to deliver the best technology solutions that will empower our clients to adapt to ever-changing
                             business environments easily and realize their goals. Customer focus is of paramount importance to us is embodied
                              in the company’s culture. At every step, it is about meeting the needs of our customers against any odds.nologies and 
                              extensive experience in different industry verticals, we strive to deliver scalable, secure and reliable</p>
                        <div className='btn-wrap'>
                            <Button className='btn-secondary'>Join Us</Button>
                        </div>


                    </div>

                </div>
            </Container>
        </section>
    );
};

export default CoreValue;
