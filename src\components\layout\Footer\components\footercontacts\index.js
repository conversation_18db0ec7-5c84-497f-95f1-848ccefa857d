import React from "react";
import Image from 'next/image';
import Link from 'next/link';


function FooterContacts() {
    const email = '<EMAIL>';
    return (
        <div className='footer-left-cont-wrap'>
            <div className='footer-cont-left'>
                <div className='footer-leftcont-row mb-60'>
                    <Link href="/"><Image
                        src="/images/beco-footer-logo.png"
                        alt="beco footer logo"
                        width={0}
                        height={0}
                        style={{ width: '188px', height: 'auto' }}
                        className="footer-logo"
                    />
                    </Link>
                </div>
                <div className='footer-leftcont-row'>
                    <span>CWS 12 , Room No 17
                    <br />Geo Info Park, Kakkanad, Kochi,<br /> Ernakulam, Kerala, 682042</span>
                </div>
                <div className='footer-leftcont-row'>
                    <h4>Write us to</h4>
                    <div><a href={`mailto:${email}`} className="write-us-link">
                        <EMAIL>
                    </a></div>
                </div>
                <div className='footer-leftcont-row mb-24'>
                    <h4>Follow us</h4>
                    <div className="social-icons d-flex">
                        <a href="https://facebook.com/becoMap/" target="_blank" rel="noopener noreferrer">
                            <Image
                                src="/images/icons/facebook.png"
                                alt="facebook"
                                width={22}
                                height={22}
                                className="social-logo"
                            />
                        </a>
                        <a href="https://www.linkedin.com/company/beco-technologies-pvt-ltd/" target="_blank" rel="noopener noreferrer">
                            <Image
                                src="/images/icons/linkedin.png"
                                alt="linkedin"
                                width={22}
                                height={22}
                                className="social-logo"
                            />
                        </a>
                        <a href="https://x.com/beco_map" target="_blank" rel="noopener noreferrer">
                            <Image
                                src="/images/icons/twitter.png"
                                alt="twitter"
                                width={22}
                                height={22}
                                className="social-logo"
                            />
                        </a>
                        <a href="https://www.instagram.com/becomap_/" target="_blank" rel="noopener noreferrer">
                            <Image
                                src="/images/icons/instagram.png"
                                alt="instagram"
                                width={22}
                                height={22}
                                className="social-logo"
                            />
                        </a>
                    </div>
                </div>
                <div className='footer-leftcont-row'>
                    <p className="copyright-text">
                        © 2024 GlobeCo technologies Pvt Ltd.
                    </p>
                </div>
            </div>
        </div>
    );
}

export default FooterContacts;