.core-value-wrap {
    background-color: $primary-color;
}
.corevalue-img-wrap {
    width: 249px;
    height: 249px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: $round;
    position: relative;
    &.corevalue-imgwrap-gray {
        background-color: rgba(240, 240, 240, 1);
    }
    &.corevalue-imgwrap-secondary-color {
        background-color: $secondary-color;
    }
    &.corevalue-imgwrap-black {
        background-color: black;
    }
}
.corevalue-right {
    padding-left: 30px;
    flex: 1;
    .btn {
        min-width: 232px;
    }
}
.corevalue-left {
    padding: 0 190px;
}
.corevalue-img-box {
    position: relative;
    margin-bottom: -50px;
    &:last-child {
        margin-bottom: 0;
    }
    .core-value-img-text {
        position: absolute;
        z-index: 1;
        background: rgba(79, 205, 112, 1);
        padding: 0 26px;
        border-radius: 30px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        top: 50%;
        margin-top: -24px;
        font-family: $font-bold;
        width: max-content;
        &.core-value-img-text-left {
            right: 100%;
            margin-right: -40px;
        }
        &.core-value-img-text-right {
            left: 100%;
            margin-left: -40px;
        }
    }
}
