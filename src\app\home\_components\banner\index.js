import React from "react";
import { Row, Col, Container } from "react-bootstrap";
import Button from "react-bootstrap/Button";
import Image from "next/image";

const HomeBanner = () => {
  return (
    <div className="homeb-banner-section p-relative">
      <video
        id="background-video"
        autoPlay
        loop
        muted
      >
        <source src="/images/becomap-video.mp4" type="video/mp4" />
      </video>
      <Container fluid={"xl"} className="banner-content">
          <h2 className="banner-title">Versatile Indoor Mapping Software</h2>
        {/* <p>Powerful Indoor navigation and location services for built-up areas; we help
                        you gain more control and visibility while providing convenience through our solutions.</p> */}
        <div className="button-wrap">
          <Button variant="primary" className="btn-secondary">
            Get a Demo
          </Button>
        </div>
      </Container>
    </div>
  );
};

export default HomeBanner;
