// .banner-row {
//     padding: 0 50px;
// }

// .banner-left {
//     padding-right: 7px;

//     .banner-content {
//         background-image: url("/images/banner-vector-bg.png");
//         background-color: $secondary-color;
//         border-radius: 42px 0 0 42px;
//         padding-left: 141px;
//         display: flex;
//         justify-content: center;
//         flex-direction: column;
//         height: 100%;
//         background-position: right 30px bottom 30px;
//         background-repeat: no-repeat;
//         p {
//             font-size: 16px;
//             font-family: $font-medium;
//             line-height: 30px;
//             margin-bottom: 22px;
//             max-width: 577px;
//         }
//     }
// }
// h2.banner-title {
//     font-size: 55px;
//     line-height: 60px;
//     margin-bottom: 13px;
//     font-family: $font-medium;
//     max-width: 577px;
// }
// .banner-right {
//     padding-left: 7px;
//     border-radius: 0 42px 42px 0;
//     overflow: hidden;
// }

// .banner-center-round {
//     position: absolute;
//     left: 50%;
//     bottom: -63px;
//     margin-left: -63px;
//     background: #fff;
//     border-radius: 50%;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     img {
//         animation: updown 2s ease infinite;
//         // cursor: pointer;
//     }
// }
// @keyframes updown {
//     0% {
//       transform: translateY(-5%);
//     }

//     50% {
//       transform: translateY(5%);
//     }

//     100% {
//       transform: translateY(-5%);
//     }
//   }

// @media (max-width: 1400px) {
//     .banner-left {
//         padding-right: 7px;

//         .banner-content {
//             padding-left: 50px;
//             p {
//                 font-size: 15px;
//                 max-width: 500px;
//             }
//         }
//     }
//     h2.banner-title {
//         font-size: 45px;
//         line-height: 50px;
//         max-width: 500px;
//     }
//     .banner-right img {
//         height: 420px !important;
//     }
// }
// @media (max-width: 767px) {
//     h2.banner-title {
//         font-size: 25px;
//         line-height: 30px;
//         max-width: 100%;
//     }
//     .banner-left {
//         padding-right: 0;
//         .banner-content {
//             padding: 20px;
//             border-radius: 10px 10px 0 0;
//         }
//     }
//     .banner-row {
//         flex-direction: column;
//     }
//     .banner-right {
//         padding-left: 0;
//         border-radius: 0 0 10px 10px;
//         height: 300px;
//         img {
//             height: inherit !important;
//             object-fit: cover;
//         }
//     }

// }

.homeb-banner-section {
  --banner-height: 836px;
  margin-top: -66px;
  position: relative;
  max-height: var(--banner-height);
  

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-image: url("/images/home-banner-bg.png");
    background-size: cover;
    background-position: center;
    z-index: 2;
    pointer-events: none;
  }

  #background-video {
    width: 100%;
    height: auto;
    max-height: var(--banner-height);
    object-fit: cover;
    position: relative;
    z-index: 1;
    pointer-events: none;
    @media (max-width: 767px) {
      min-height: 420px;
    }
  }

  .banner-content {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    right: auto;
    bottom: 0;
    height: 100%;
    width: 100%;
    z-index: 3;
    display: flex;
    justify-content: center;
    flex-direction: column;
    text-align: start;
    padding-right: 40%;
    @media (max-width: 1199px) {
      padding-left: 30px;
    }

    @media (max-width: 991px) {
      padding-right: 30px;
      text-align: center;
      .btn {
        min-width: 190px;
      }
    }
    .banner-title {
      font-family: $font-bold;
      font-size: 50px;
      font-weight: 700;
      line-height: 60px;
      text-align: left;
      margin-bottom: 30px;
      position: relative;
      z-index: 2;
      @media (max-width: 1199px) {
        font-size: 40px;
        line-height: 45px;
      }
      @media (max-width: 991px) {
        font-size: 34px;
        line-height: 38px;
        margin-bottom: 15px;
      text-align: center;

      }
      @media (max-width: 767px) {
        font-size: 28px;
        line-height: 30px;
        margin-top: 50px;
      }
      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 250%;
        z-index: -1;
        background: radial-gradient(#e9e9eb 1.99%, rgba(232, 232, 232, 0) 67%);
      }
    }
  }
}
