# www.becomap.com

60min webiste project

* ```git clone https://github.com/sayone-tech/becomap-website.git```
* run ```npm install``` or ```yarn``` to install all depencies
* run ```next dev``` or ```npm run dev``` or ```yarn dev``` and visit ```http:\\localhost:3000```

You can start editing the page by modifying ```app/page.js```. The page auto-updates as you edit the file.

## Project Dependencies
* ```Next : 14.2.13```
* ```Node : 20.17.0```
* ```Npm : 10.8.2```

```

### Build

* ```next build```  or  ```yarn build```  or  ```npm run build``` 
* build saved to the ```build``` folder