.sc-sec {
  position: relative;
  padding: 80px 0;
  overflow: hidden;
  &.shopping-mall {
    background-position: top;
    background-size: 1924px 398px;
    background-repeat: no-repeat;
    @media (min-width: 992px) {
      background-image: url("/images/shop-bg.png");
    }

    @media (max-width: 1199px) {
      background-size: 1524px 315px;
    }
  }
  &.healthcare {
    background-position: top;
    background-size: 1936px 389px;
    background-repeat: no-repeat;
    @media (min-width: 992px) {
      background-image: url("/images/hosp-bg.png");
    }

    @media (max-width: 1199px) {
      background-size: 1536px 309px;
    }
  }
  @media (max-width: 1199px) {
    padding: 60px 30px 80px;
  }

  @media (max-width: 991px) {
    padding: 30px 30px 50px;
  }
  * {
    position: relative;
  }
  img {
    width: 100%;
    height: 100%;
  }

  .img-wrap {
    @media (max-width: 991px) {
      max-width: 370px;
      margin: 0 auto;
    }
  }

  .solution-details {
    .main-title {
      margin-bottom: 20px !important;
      @media (max-width: 991px) {
        text-align: center;
        max-width: 620px;
        margin: 0 auto;
      }
      @media (max-width: 1399px) {
        font-size: 36px;
        line-height: 42px;
        margin-bottom: 8px !important;
      }
      @media (max-width: 1199px) {
        font-size: 32px;
        line-height: 32px;
      }
      @media (max-width: 991px) {
        font-size: 26px;
        line-height: 30px;
      }
    }
    p {
      margin-bottom: 0 !important;
      line-height: 26px;
      @media (max-width: 991px) {
        text-align: center;
        max-width: 520px;
        margin: 0 auto;
      }
    }
  }
}

.bg-im-s {
  position: absolute;
  background-image: url("/images/ind-im-part.png");
  background-repeat: no-repeat;
  top: 80px;
  background-position: right top;
  width: 100%;
  height: 100%;
  @media (max-width: 1199px) {
    display: none;
  }
}

.bg-lst-prt {
  position: absolute;
  background-image: url("/images/ind-im-part-2.png");
  background-repeat: no-repeat;
  bottom: 0;
  z-index: 2;
  left: auto;
  right: 0;
  background-position: bottom right;
  width: 50%;
  height: 100%;
  @media (max-width: 1199px) {
    display: none;
  }
}

.bg-anim-ort {
  position: absolute;
  top: -12px;
  right: 0;
  height: auto !important;

  @media (max-width: 767px) {
    display: none;
  }

  &.bg-shoppingmall {
    top: 105px;
    @media (max-width: 1399px) {
      top: 90px;
    }
    @media (max-width: 1199px) {
      top: 27px;
    }
  }
  &.bg-hospital {
    top: 56px;
    @media (max-width: 1399px) {
      // top: -10px;
    }
  }
}

.navigation-point {
  width: 30px;
  height: 30px;
  background-color: $secondary-color;
  border-radius: 50%;
  position: absolute;
  z-index: 3;
  animation: pulse 2s infinite;

  &::before {
    content: "";
    position: absolute;
    width: 15px;
    height: 15px;
    background-color: transparent;
    border: 2px solid #1c1e1c;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 $secondary-color;
    }
    70% {
      box-shadow: 0 0 0 25px rgba(168, 63, 57, 0);
    }
    100% {
      box-shadow: 0 0 0 50px rgba(168, 63, 57, 0);
    }
  }
}

.event-sec {
  background-image: url("/images/event-bg.png"), url("/images/event-bg-1.png");
  background-repeat: no-repeat, no-repeat;
  background-position: top center, bottom center;
  background-size: 1918px 123.5px, 1962px 400px;
  padding: 120px 0 60px;
  @media (max-width: 1199px) {
    background-size: 1318px 84px, 1318px 269px;
  }
  @media (max-width: 991px) {
  background-image: url("/images/event-bg.png");
    
  }
  .srv-icon-bg {
    width: 54px;
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-bottom: 18px;
  }
}
