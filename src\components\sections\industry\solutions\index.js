import ClientSlider from "@/app/company/_components/companybanner/components/clients";
import Image from "next/image";
import { Col, Container, Row } from "react-bootstrap";

const SolutionItem = ({ title, description, iconUrl, color }) => (
  <Col lg="4" className="mb-3 mb-lg-0">
    <div className="solution-item-box">
      <div className="solution-icon-wrap d-flex flex-column align-items-start">
        <span className={`srv-icon-bg`} style={{
        backgroundColor: `${color}`,
        }}>
          <Image
            src={iconUrl}
            alt={title}
            width={28}
            height={28}
            className="solution-icon-img"
          />
        </span>
        <h4 className="small-head">{title}</h4>
      </div>
      <p className="mb-0">{description}</p>
    </div>
  </Col>
);
const IndustrySolutions = ({ title, image, solutions ,description, imageProperty }) => {
  return (
    <section className="section pt-0">
      <Container>
        <div className="soultion-wrapper"
          style={{
            background: imageProperty.backgroundColor === "gradient" ? "linear-gradient(94.69deg, #f3f3f3 46.57%, #ffffff 100.4%)" : "rgba(243, 243, 243, 1)",
          }}
          >
          <Row className={`align-items-${imageProperty.justifyContentParent}`}>
            <Col xl={6} lg={7}>
              <div className="soultion-details mb-5 mb-lg-0">
                <h2 className="main-title mb-3">
                  {title}
                </h2>
                <p className="mb-0">
                  {description}
                </p>
              </div>
            </Col>
            <Col xl={6} lg={5}>
              <div className={`d-flex d-flex align-items-center justify-content-${imageProperty.justifyContent} sl-img-wrap`}
                style={{
                  marginTop: imageProperty.top,
                  marginBottom: imageProperty.bottom,
                }}
              >
                <Image
                  src={image.src}
                  alt="banner-image"
                  width={imageProperty.width}
                  height={imageProperty.height}
                 
                />
              </div>
            </Col>
          </Row>
        </div>
        <Row>
          {solutions.map((item, index) => (
            <SolutionItem
              key={index}
              title={item.title}
              description={item.description}
              iconUrl={item.iconUrl}
              color={item.color}
            />
          ))}
        </Row>
      </Container>
    </section>
  );
};

export default IndustrySolutions;
