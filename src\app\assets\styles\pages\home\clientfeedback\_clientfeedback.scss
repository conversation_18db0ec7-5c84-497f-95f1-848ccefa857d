.client-feedback-section {
    background-color: #F4F5F7;
    background-repeat: no-repeat;
    background-image: url("/images/testi-bg.png"), url("/images/testi-bg-2.png");
    background-position: left center, right top;
    background-size: auto;
}
.key-points-wrap {
    max-width: 80%;
    h4 {
        font-size: 25px;
        font-family: $font-bold;
    }
    ul {
        margin: 0;
        padding: 0;
        margin-top: 24px;
        li {
            font-size: 18px;
            list-style: none;
            margin-bottom: 27px;
            display: flex;
            align-items: center;
            line-height: 22px;
            @media (max-width:1199px){
                font-size: 16px;
                line-height: 26px;
                margin-bottom: 20px;
            }
            .list-tick-img {
                margin-right: 14px;
                display: inline-block;
                position: relative;
                top: -2px;
            }
        }
    }
}
.ind-pn {
    @media (max-width: 991px) {
        .key-points-wrap {
            max-width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            
        }
    }
}
.client-feedback-right {
    .section-head {
        margin-bottom: 40px;
    }
    @media (max-width:1199px){
        .main-title {
            font-size: 38px;
            line-height: 40px;
        }
    }
}
.testimonialsec-bg-right-bottom {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 0;
}
.testimonial-content-wrap {
    padding: 0 30px 0 83px;
    @media(max-width:1199px){
    padding: 0 30px 0 53px;

    }
}
.testimonial-cont {
    font-size: 22px;
    font-family: $font-medium;
    line-height: 33px;
    font-weight: normal;
    margin: 24px 0;
    @media(max-width:1199px){
        font-size: 18px;
        line-height: 28px;
    }
}

@media (max-width: 1400px) {
    .client-feedback-left img {
        max-width: 100% !important;
        height: auto !important;
    }
    .client-feedback-left {
        display: flex;
        align-items: center;
    }
    .client-feedback-right {
        position: relative;
        z-index: 2;
    }
}
@media (max-width: 991px) {
    .client-feedback-section {
        background-size: 50%;
        overflow: hidden;
    }
    .client-feedback-slider-wrap .quotes-img {
        left: 0 !important;
        top: -40px !important;
        img {
            width: 30px !important;
        }
    }
    .testimonialsec-bg-right-bottom {
        display: none;
    }
    .testimonial-cont {
        font-size: 18px;
        margin: 15px 0;
    }
    .key-points-wrap {
        h4 {
            font-size: 20px;
        }
        ul {
            margin-top: 15px;
            li {
                font-size: 15px;
                margin-bottom: 17px;
                line-height: 20px;
                .list-tick-img {
                    margin-right: 10px;
                    svg {
                        width: 24px;
                        height: 24px;
                    }
                }
            }
        }
    }
 
}
