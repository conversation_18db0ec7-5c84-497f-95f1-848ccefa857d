import Image from "next/image";

const ClientSayContent = ({ writer, content, imgUrl, position }) => {
    return (
        <div className="company-clientsay-box p-relative">
            <div className="paper-clip-top">
            <Image src="/images/paper-pin.png" alt='paper clip' width="50" height="50" />
            </div>
            <div className="quote-wrap">
                <span>
                    <svg width="65" height="52" viewBox="0 0 65 52" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M65 8.12147C55.2645 12.6334 50.3968 17.9349 50.3968 24.026C54.5463 24.4772 57.9777 26.0752 60.6909 28.82C63.404 31.5647 64.7606 34.7418 64.7606 38.3514C64.7606 42.1866 63.4439 45.4201 60.8105 48.0521C58.1772 50.684 54.8655 52 50.8756 52C46.4068 52 42.5366 50.2892 39.2648 46.8677C35.993 43.4461 34.3571 39.2914 34.3571 34.4035C34.3571 19.7396 43.0552 8.27191 60.4515 0L65 8.12147ZM30.6429 8.12147C20.8275 12.6334 15.9199 17.9349 15.9199 24.026C20.1493 24.4772 23.6205 26.0752 26.3337 28.82C29.0469 31.5647 30.4035 34.7418 30.4035 38.3514C30.4035 42.1866 29.0668 45.4201 26.3936 48.0521C23.7203 50.684 20.3887 52 16.3987 52C11.9299 52 8.07968 50.2892 4.8478 46.8677C1.61592 43.4461 0 39.2914 0 34.4035C0 19.7396 8.65812 8.27191 25.9746 0L30.6429 8.12147Z" fill="white" fill-opacity="0.8" />
                    </svg>
                </span>
            </div>
            <p className="client-say-content">{content}</p>
            <div className="d-flex company-clientsay-authorbox">
                <div className="company-clientsay-authorbox-left">
                    <img src={imgUrl} alt='client feedback'  />
                </div>
                <div className="company-clientsay-authorbox-right">
                    <h5>{writer}</h5>
                    <p>{position}</p>
                </div>
            </div>
        </div>
    );
};

export default ClientSayContent;
