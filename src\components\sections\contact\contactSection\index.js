"use client";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON> } from "react-bootstrap";
import dynamic from "next/dynamic";
import ContactDetails from "../contactdetails";
import ContactForm from "../contactform";




const DynamicOfficeAddress = dynamic(
  () => import("../offices"), // Adjust the import path as needed
  { ssr: false }
);

const ContactSection = () => {
  return (
    <div className="contact-wrap">
      <Container className="contact-container mb-5" fluid={"xl"}>
        <Row className="contact-row d-flex">
          <Col xl={6} className="contact-left-col ">
            <ContactDetails />
          </Col>
          <Col xl={6} className="contact-right-col">
            <ContactForm />
          </Col>
        </Row>
      </Container>
      <DynamicOfficeAddress />
    </div>
  );
};

export default ContactSection;
