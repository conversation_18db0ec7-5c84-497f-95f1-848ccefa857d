"use client";
import { useEffect, useRef, useState } from "react";
// import confetti from "canvas-confetti";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { Col, Container, Row } from "react-bootstrap";
import Confetti from "react-confetti";

const ThankYou = () => {
  const [isConfettiActive, setIsConfettiActive] = useState(false);

  const router = useRouter();

  //   const drawShape = (ctx) => {
  //     ctx.beginPath();
  //     for(let i = 0; i < 22; i++) {
  //       const angle = 0.35 * i;
  //       const x = (0.2 + (1.5 * angle)) * Math.cos(angle);
  //       const y = (0.2 + (1.5 * angle)) * Math.sin(angle);
  //       ctx.lineTo(x, y);
  //     }
  //     ctx.stroke();
  //     ctx.closePath();
  //   };

  useEffect(() => {
    setIsConfettiActive(true);
  }, []);

  return (
    <section className="thanks-sec">
      <div className="header-tha">
        {isConfettiActive && (
          <Confetti
            gravity={0.05}
            wind={0.01}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: 0,
              opacity: 0.5,
            }}
          />
        )}
        {/* <canvas ref={canvasRef} style={{ position: "absolute", top: 0, left: 0, width: "100%", height: "100%", pointerEvents: "none" }} /> */}
        <Container>
          <Row className="justify-content-center align-items-center">
            <Col xl="10" lg="12">
              <h1>Thank You for Contacting Us!</h1>
              <p>We Will Get Back to You Soon.</p>
            </Col>
          </Row>
        </Container>
      </div>
      <div className="footer-tha">
      <Container fluid="lg">
          <Row className="justify-content-center align-items-center">
            <Col xl="10" lg="12">
              <div className="footer-card">
                <Row>
                  <Col md="12">
                    <div className="thank-links">
                      <h2>Discover Our Full Range of Services</h2>
                      <p>
                        Learn All About What We Offer and How We Can Tailor Our
                        Services <br />
                        To Meet Your Needs.
                      </p>
                      <ul className="links-wrap">
                        <li>
                          <Link href={"/solutions/indoor-mapping"}>
                            <Image
                              src="/images/icons/room.png"
                              width={20}
                              height={20}
                              alt="Indoor Navigation"
                            />
                            Indoor Navigation
                          </Link>
                        </li>
                        <li>
                          <Link href={"/solutions/proximity-services"}>
                            <Image
                              src="/images/icons/map.png"
                              width={20}
                              height={20}
                              alt="Proximity services"
                            />
                            Proximity services
                          </Link>
                        </li>
                        <li>
                          <Link href={"/solutions/kiosk"}>
                            <Image
                              src="/images/icons/stats.png"
                              width={20}
                              height={20}
                              alt="Kiosks"
                            />
                            Kiosks
                          </Link>
                        </li>
                        <li>
                          <Link href={"/solutions/asset-tracking"}>
                            <Image
                              src="/images/icons/product.png"
                              width={20}
                              height={20}
                              alt="Asset Tracking"
                            />
                            Asset Tracking
                          </Link>
                        </li>
                        <li>
                          <Link href={"/solutions/analytics"}>
                            <Image
                              src="/images/icons/data-analytics.png"
                              width={20}
                              height={20}
                              alt="Analytics"
                            />
                            Analytics
                          </Link>
                        </li>
                        <li>
                          <Link href={"/solutions/web-app"}>
                            <Image
                              src="/images/icons/computer.png"
                              width={20}
                              height={20}
                              alt="Web App"
                            />
                            Web App
                          </Link>
                        </li>
                      </ul>
                    </div>
                  </Col>
                  <Col lg="7">
                    <div className="thank-blog">
                      <h2>Read Our Blogs</h2>
                      <p>
                        Discover the Journey of Capital Numbers, Meet the
                        Visionaries Behind Our Success, and More. Discover the
                        Journey of Capital Numbers, Meet the Visionaries Behind
                        Our Success, and More!
                      </p>
                      <Image
                      alt="Blog"
                        src="/images/thank-blog-img.png"
                        width={100}
                        height={100}
                        style={{ width: "100%", height: "auto" }}
                      />
                      <button className="btn btn-outline" onClick={() => { router.push("/blog")}}>
                        Read Our Blogs
                      </button>
                    </div>
                  </Col>
                  <Col lg="5">
                    <div className="thank-ebook">
                      <h2>Ebook</h2>
                      <p>
                        Discover the Journey of Capital Numbers, Meet the
                        Visionaries Behind Our Success, and More!
                      </p>
                      <Image
                      alt="Ebook"
                        src="/images/ebook-1.png"
                        width={180}
                        height={147}
                      />
                      <button className="btn-secondary btn btn-primary">
                        Download Ebook
                      </button>
                    </div>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </section>
  );
};

export default ThankYou;
