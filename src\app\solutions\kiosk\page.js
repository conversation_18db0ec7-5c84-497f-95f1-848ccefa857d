import ClientSlider from "@/app/company/_components/companybanner/components/clients";
import SolutionBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
// import IndustryExperience from "../_components/IndustryExperience";
// import IndustrySecondSection from "../_components/IndustrySecondSection";
import IndustrySolutions from "../_components/solutions";
// import IndustryAccordion from "../_components/IndustryAccordion";
import IndustryServices from "../_components/IndustryServices";
import IndustryEBook from "../_components/IndustryEBook";
import FaqSection from "../_components/FaqSection";
import SolutionWhyUs from "../_components/solutionWhyUs";
import HeroImg from "@/app/assets/images/solutions/kiosk-hero.png";
import SecImg from "@/app/assets/images/solutions/becomap-care.png";
// import SecImg2 from "@/app/assets/images/industry/mall-sec-im-2.png";
// import SecImg3 from "@/app/assets/images/industry/mall-help-illu.png";
// import SecImg4 from "@/app/assets/images/industry/mall-im-sec-2-2.png";
// import BgImg from "@/app/assets/images/industry/indu-accr-bg.png";
import TalkToOurTeam from "@/app/components/talktoourteam";
import Blogs from "@/app/home/<USER>/blogs";
import { Col, Container, Row } from "react-bootstrap";
import Image from "next/image";

import SolutionWhyUsBg from "@/app/assets/images/solutions/why-bg.png";
import WhySubImage from "@/app/assets/images/solutions/kiosk/why-sub-img.png";
const mallNavigationFAQ = [
  {
    question: "How does indoor navigation work?",
    answer:
      "We create detailed digital maps of your mall and integrate them with our navigation software. This helps customers find stores and services easily.",
  },
  {
    question: "What are interactive kiosks?",
    answer:
      "Interactive kiosks are touch-screen systems found in malls that help users to easily find store locations, events, and utilities. These kiosks help visitors to navigate the mall easily and help in discovery and foot traffic to more stores in your mall.",
  },
  {
    question: "How does Becomap benefit the mall?",
    answer:
      "Becomap increases sales and foot traffic by helping shoppers easily navigate the mall and discover promotions, encouraging more in-store visits and driving higher sales.",
  },
];

const SOLUTIONS = [
  {
    title: "Touchscreen Interface",
    description:
      "Visitors can interact with maps through an easy-to-use, touch-friendly interface, making it simple to search for locations and explore the indoor space.",
    iconUrl: "/images/icons/route.png",
    color: "#34A853",
  },
  {
    title: "Instant Access to Maps",
    description:
      "No need for personal devices or downloads—the kiosk provides immediate access to maps, allowing visitors to find their way without extra steps.",
    iconUrl: "/images/icons/wifi.png",
    color: "#34A853",
  },
  {
    title: "Up-to-Date Information",
    description:
      "The kiosk displays current maps of the indoor space, making sure that users always have the latest layout information.",
    iconUrl: "/images/icons/human-resource.png",
    color: "#34A853",
  },
];

const accordionData = [
  {
    title: "Easily Integrate Maps",
    content:
      "Integrate map of your retail space into existing apps to effortlessly guide shoppers to their favorite stores, restaurants, and services via the shortest route.",
  },
  {
    title: "Multi-Language Support",
    content:
      "Make your mall map accessible to all visitors by offering it in multiple languages. beComap allows you to provide directions and information in the shopper’s preferred language, making the mall experience more personalized and user-friendly. This feature is particularly valuable in diverse or tourist-heavy locations.",
  },
  {
    title: "Give Updates on Store Offers",
    content:
      "Keep shoppers in the loop with instant updates on store deals, upcoming events, and essential services, improving their overall shopping experience.",
  },
  {
    title: "Less Searching, More Shopping",
    content:
      "Navigate in a flash with our system—find the fastest route and shortest path from parking to stores, or multi-stop routes to plan your shopping with less walking and more items on your cart.",
  },
];

const ServiceData = [
  {
    title: "1. Choose the Right Location",
    content:
      "Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamles Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies. Offer personalized services, real-time updates, and seamles Develop an ultra-modern travel environment that entices travelers to revisit your airport for its advanced technologies.",
    image: "/images/choose-the-right-location.png",
  },
  {
    title: "2. Create Digital Maps",
    content:
      "Next, we create detailed digital maps of your space. These maps are carefully developed to show important areas such as entrances, rooms, service zones, and facilities like restrooms or customer service desks. We also make sure these maps are regularly updated, so any changes to your layout, such as new rooms or temporary closures, are reflected accurately. Visitors will always have the most current information, allowing them to navigate your space with confidence.",
    image: "/images/create-digital-maps.png",
  },
  {
    title: "3. Develop the Kiosk Interface",
    content:
      "Once the maps are ready, we focus on designing a simple and user-friendly interface for the kiosks. The goal is to make it easy for visitors to access maps and follow step-by-step directions directly from the screen. The interface is clear with names and zones, allowing visitors to quickly search for specific locations and view their routes. Whether they’re looking for a store, a room, or a service area, the kiosk guides them with clear instructions, helping them move around without confusion.",
    image: "/images/develop-the-kiosk-interface.png",
  },
];

const WHY_DATA = [
  {
    title: "Access the Map on the Kiosk",
    description:
      "Visitors can interact directly with the kiosk screen, providing a touch-friendly interface that displays the full indoor map.",
    iconUrl: "/images/icons/access-map.png",
    activeIconUrl: "/images/icons/access-map-active.png",
    color: "#34A853",
  },
  {
    title: "Search for a Location",
    description:
      "Users can search for specific locations like stores, rooms, or service areas right from the kiosk.",
    iconUrl: "/images/icons/search-map.png",
    activeIconUrl: "/images/icons/search-map-active.png",
    color: "#34A853",
  },
  {
    title: "Explore the Map",
    description:
      "Users can pan, zoom, and explore the entire indoor space to understand the layout and available services.",
    iconUrl: "/images/icons/explore-map.png",
    activeIconUrl: "/images/icons/explore-map-active.png",
    color: "#34A853",
  },
  {
    title: "Navigate to Destination",
    description:
      "The kiosk provides step-by-step directions on-screen, guiding visitors to their desired location within the indoor space.",
    iconUrl: "/images/icons/navigate-map.png",
    activeIconUrl: "/images/icons/navigate-map-active.png",
    color: "#34A853",
  },
];

const Kiosk = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <SolutionBanner
        bgImage={HeroImg}
        title="Make Indoor Navigation <br /> Accessible with <br /> beComap’s Kiosk"
        description="beComap’s Kiosk offers a self-service solution for indoor <br /> navigation, allowing visitors to easily find their way in malls, <br /> airports, hospitals, or event venues, without requiring a personal <br /> device."
      />
      <IndustryClients />
      <SolutionWhyUs
        title={"What Is Kiosk?"}
        description={
          "beComap’s Kiosk helps visitors to access interactive indoor maps and get directions without needing a smartphone or any other device. Located in high-traffic areas, the kiosk provides an easy-to-use interface that helps users search for destinations, view maps, and receive clear directions right on the screen"
        }
        bgImage={SolutionWhyUsBg}
        subImage={WhySubImage}
        whyData={WHY_DATA}
      />
      <IndustrySolutions
        image={SecImg}
        solutions={SOLUTIONS}
        title={"What Makes the Kiosk Effective?"}
        description={{
          part1:
            "The kiosk offers a simple way for visitors to find their way without needing a smartphone. Placed in key areas of your venue, it lets users search for locations, view maps, get location details and get directions without having to download an app or use their personal device. This makes it especially useful for people who prefer not to rely on their phones or may not have access to one.",
          part2:
            "With its easy-to-use interface, the kiosk helps visitors quickly find important areas like entrances, restrooms, stores, or departments. This reduces the chance of them getting lost in a large space, saving time and frustration. The kiosk is particularly helpful in busy places like malls, airports, and hospitals, where quick access to information is needed.",
          part3:
            "For venue owners, the kiosk reduces the need for staff to assist with directions and helps visitors navigate on their own. It's an efficient solution for improving the visitor experience by providing clear, self-service options for finding their way through large or complex spaces.",
        }}
      />

      {/* 
      <IndustrySecondSection
        image={SecImg4}
        routeType="shoppingmall"
        title={"No More Mazes. Get Shoppers Straight to the stores."}
        description="Lost customers and missed sales opportunities are things of the past.  Becomap's Retail Wayfinding Solution is here to change that. Our system turns confusion into clarity, allowing your mall visitors to navigate your store with ease. They can quickly find what they’re looking for, explore the latest deals, and stay informed about any changes—all without frustration. With Becomap, your customers enjoy a continuous shopping experience, and you benefit from happier clients and increased foot traffic and sales."
      />
      <IndustrySolutions
        imageProperty={{
          width: 441,
          height: 505,
          top: "-120px",
          bottom: "0",
          justifyContentParent: "end",
          justifyContent: "end",
          backgroundColor: "gradient",
        }}
        image={SecImg}
        solutions={SOLUTIONS}
        title={"Mall Experience like never before"}
        description="Lost customers and missed sales opportunities are things of the past.  Becomap's Retail Wayfinding Solution is here to change that. Our system turns confusion into clarity, allowing your mall visitors to navigate your store with ease. They can quickly find what they’re looking for, explore the latest deals, and stay informed about any changes—all without frustration. With Becomap, your customers enjoy a continuous shopping experience, and you benefit from happier clients and increased foot traffic and sales."
      />
      <IndustryExperience
        image={SecImg2}
        title="Mall Navigation Made Easy"
        description="Our solution makes navigating your mall simple and stress-free. It offers detailed info on essential services like restrooms, ATMs, and customer service desks through the app. Shoppers can use easy turn-by-turn directions to find stores and services quickly. By enhancing their overall experience and reducing frustration, more visitors will be encouraged to spend time in our mall, increasing foot traffic and driving sales."
      />
      <IndustryAccordion
        title={"Discover 3X More Stores!"}
        bgImage={BgImg}
        description={
          "With Becomap, shoppers get a complete list of all the stores in your mall, including detailed information and current offers. Now, they can explore more shops than ever before and find the best deals, turning every visit into an exciting shopping adventure and driving more foot traffic to your mall."
        }
        accordionData={accordionData}
        videoSrc={"/videos/mall.mp4"}
      />
    
      <IndustryHelp
        title="Why World’s largest venues trust beComap"
        description="beComap changes the way you think about retail navigation & engagement. Our platform simplifies navigation, attracts and retains customers, and optimizes operations to improve the overall shopping journey."
        image={SecImg3}
        points={[
          "Best-in-class indoor mapping for malls",
          "8 years on active R&D in location intelligence",
          "Optimized for use on mobile devices, web browsers, and kiosks",
          "Easy integration with existing apps",
          "Web-based navigation without any downloads",
          "Multiple language support",
        ]}
      />
 */}

      <IndustryServices
        title="How We Set Up Your Kiosk for Indoor Navigation"
        description="Setting up your kiosk for indoor navigation includes several key steps to ensure a smooth experience for visitors. From choosing the best location for the kiosk to developing the map interface, we handle everything to make it easy for users to navigate your indoor space."
        image={"/images/kiosk-dummy.png"}
        serviceData={ServiceData}
      />
      <section
        className="section primary-bg"
        style={{
          backgroundImage: `url("/images/solution-bg.png")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          minHeight: "680px",
        }}
      >
        <Container>
          <Row className="align-items-center">
            <Col xs={7}>
              <Image
                src={"/images/solution-sec.png"}
                alt="indoor mapping software"
                width={550}
                height={450}
                style={{ width: "100%", height: "auto" }}
              />
            </Col>
            <Col xs={5}>
              <div className="section-head">
                <span
                  className="mb-4"
                  style={{
                    fontWeight: 600,
                    fontSize: "18px",
                  }}
                >
                  Case Study
                </span>
                <h2 className="main-title mb-3">Indoor mapping software</h2>
                <p>
                  Develop an ultra-modern travel environment that ree
                  experiences. Set new standards in travel convenience and
                  satisfaction through innovation, sustainability, and
                  operational efficiency.
                </p>
              </div>
              <button className="btn btn-dark mt-4">Learn More</button>
            </Col>
          </Row>
        </Container>
      </section>
      <IndustryEBook
        image={"/images/mall-ebook-bg.png"}
        title={"Retail In Action"}
        description={`<p>According to ACI, global passenger traffic will reach 9.7 billion this year.</p> <p>Learn how to deliver a smooth travel experience with indoor maps in our helpful retail guide.</p>`}
      />
      <Blogs />
      <TalkToOurTeam />
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={mallNavigationFAQ}
      />
    </div>
  );
};

export default Kiosk;
