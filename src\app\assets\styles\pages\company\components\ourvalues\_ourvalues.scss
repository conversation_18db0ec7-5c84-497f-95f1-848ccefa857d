.company-values-wrap {
  background-image: url("/images/ourvalues-bg.png");
  background-color: $primary-color;
  @media (max-width: 1199px) {
    padding: 50px 0;

    .company-value-head {
      margin-bottom: 40px;
    }
  }
}
.value-box {
  background: rgba(64, 206, 102, 1);
  padding: 39px 34px;
  border-radius: 30px;
  text-align: center;
  height: 100%;
  @media (max-width: 1199px) {
    padding: 29px 24px;
  }
}
.company-values-wrap {
  .container {
    max-width: 1060px;
  }
}
.company-value-head {
  text-align: center;
  max-width: 900px;
  margin: 0 auto 60px;
}
.value-icon {
  margin-bottom: 33px;
  span {
    display: inline-flex;
    width: 65px;
    height: 65px;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: white;
  }
}
.value-col {
  @media (min-width: 1199px) {
    padding: 0 18px;
  }

  @media (max-width: 767px) {
    margin-bottom: 15px;
  }
}
