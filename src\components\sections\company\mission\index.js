import React from 'react';
import Image from 'next/image';

import { Row, Col, Container } from 'react-bootstrap';
const OurMission = () => {
    return (
        <section className='section company-mission-wrap'>
            <Container fluid="lg">
                <div className='d-flex mission-row justify-content-between flex-lg-row flex-column-reverse'>
                    <div className='mission-left'>
                        <h2 className="main-title mb-xl-4">
                        Our Mission
                        </h2>
                        <p className=' mb-xl-5 mb-4'>At beComap, our mission is to provide best-in-class tools that create functional, effective, and affordable indoor navigation solutions for any environment. As a global technology leader in indoor location technology, we are committed to delivering reliable experiences that smoothly connect people with buildings on a large scale. We strive to help businesses and improve user interactions with their physical spaces, making sure that navigating indoors is both simple and efficient.</p>
                        <div className='mission-author'>
                            <h4>Vaishak C P</h4>
                            <h6>Co-Founder & CEO</h6>
                        </div>
                    </div>
                    <div className='mission-right'>
                    <Image src='/images/mission-img.png' alt='career' width="399" height="399" style={{ width: '100%' }} />
                    </div>
                </div>
            </Container>
        </section>
    );
};

export default OurMission;
