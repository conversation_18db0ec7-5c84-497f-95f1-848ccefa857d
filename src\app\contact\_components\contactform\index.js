import React, { useRef, useState } from "react";
import { formSubmit } from "@/app/action/action";
import { Container, Row, Col, Form, Button } from "react-bootstrap";
import { Formik } from "formik";
import { useRouter } from "next/navigation";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/bootstrap.css";

const ContactForm = () => {
  const router = useRouter();
  const [phone, setPhone] = useState("");
  const [formStatus, setFormStatus] = useState(false);

  return (
    <div className="contact-wrap contact-form-wrap">
      <Container className="contact-container">
        <div className="client-feedback-right flex-1">
          <div className="section-head">
            <h2 className="main-title mb-xl-4">
              Ready to unlock the power of location?
            </h2>
            <p>
              Discover the power of indoor positioning and tracking - contact us
              today!
            </p>
          </div>
          <div className="talkto-form-wrap">
            <Formik
              initialValues={{
                name: "",
                email: "",
                phone_number: "",
                option: "",
                message: "",
              }}
              validate={(values) => {
                const errors = {};
                if (!values.name) errors.name = "Name is required";
                if (!values.email) errors.email = "Email is Required";
                else if (
                  !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)
                ) {
                  errors.email = "Invalid email address";
                }
                if (!values.option) errors.option = "Please select an option";
                if (!values.message) errors.message = "Message is required";
                return errors;
              }}
              onSubmit={async (values, { setSubmitting, resetForm }) => {
                try {
                  const status = await formSubmit(values, "contact-sales");
                  if (status === 201) {
                    setFormStatus(true); // Set form status to submitted
                    setSubmitting(false);
                    router.push("/thankyou");
                    setTimeout(() => {
                      setFormStatus(false);
                      setSubmitting(false);
                      resetForm();
                    }, 5000);
                  } else {
                    alert(`Form submission failed with status: ${status}`);
                  }
                } catch (error) {
                  alert(`Form submission failed: ${error.message}`);
                }
              }}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                handleSubmit,
                isSubmitting,
                setFieldValue,
              }) => (
                <Form onSubmit={handleSubmit}>
                  <Row className="talkto-form-row">
                    <Col md={12}>
                      <Form.Group className="talkto-form-item">
                        <Form.Control
                          type="text"
                          name="name"
                          placeholder="Name"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          value={values.name}
                          isInvalid={errors.name && touched.name}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors.name}
                        </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="talkto-form-item">
                        <Form.Control
                          type="text"
                          name="email"
                          placeholder="Email Address*"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          value={values.email}
                          isInvalid={errors.email && touched.email}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors.email}
                        </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="talkto-form-item">
                        <PhoneInput
                          inputProps={{
                            name: "phone_number",
                            onBlur: handleBlur,
                          }}
                          placeholder="Phone Number*"
                          country={"us"}
                          enableSearch
                          onChange={(phone) =>
                            setFieldValue("phone_number", phone)
                          }
                          value={phone}
                        />
                        {errors.phone_number && touched.phone_number && (
                          <div className="invalid-feedback d-block">
                            {errors.phone_number}
                          </div>
                        )}
                      </Form.Group>
                    </Col>
                    <Col md={12}>
                      <Form.Group className="talkto-form-item p-relative">
                        <Form.Select
                          name="option"
                          value={values.option}
                          onChange={(e) =>
                            setFieldValue("option", e.target.value)
                          }
                          onBlur={handleBlur}
                          isInvalid={touched.option && !!errors.option}
                        >
                          <option value="">Your inquiry*</option>
                          <option value="Indoor navigation">
                            Indoor navigation
                          </option>
                          <option value="Asset tracking">Asset tracking</option>
                          <option value="General Inquiry">
                            General Inquiry
                          </option>
                        </Form.Select>
                        <Form.Control.Feedback type="invalid">
                          {errors.option}
                        </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                    <Col md={12}>
                      <Form.Group className="talkto-form-item">
                        <Form.Control
                          as="textarea"
                          name="message"
                          rows={3}
                          placeholder="Project Description"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          value={values.message}
                          isInvalid={errors.message && touched.message}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors.message}
                        </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                    <Col md={12}>
                      <Button
                        variant="dark"
                        type="submit"
                        className={`form-btn ${isSubmitting ? "loading" : ""}`}
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <div class="loader">
                            <div class="dot-elastic"></div>
                          </div>
                        ) : formStatus ? (
                          <span>Submitted</span>
                        ) : (
                          <span>Talk to our Team</span>
                        )}
                      </Button>
                    </Col>
                  </Row>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default ContactForm;
