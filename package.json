{"name": "becomap.com", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.7.7", "bootstrap": "^5.3.3", "canvas-confetti": "^1.9.3", "formik": "^2.4.6", "next": "14.2.13", "next-share": "^0.27.0", "react": "^18", "react-bootstrap": "^2.10.4", "react-canvas-confetti": "^2.0.7", "react-confetti": "^6.1.0", "react-dom": "^18", "react-leaflet": "^4.2.1", "react-loading-skeleton": "^3.5.0", "react-phone-input-2": "^2.15.1", "react-spring": "^9.7.4", "sharp": "^0.33.5", "swiper": "^11.1.14", "swr": "^2.2.5"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.13", "file-loader": "^6.2.0", "sass": "^1.77.6"}}