
import { TalkToOurTeam } from "@/components/sections";
import BlogList from "@/components/sections/blog/blogList/blogList";



export const metadata = {
  metadataBase: new URL('https://becomap.com'),
  title: 'Resources Indoor Navigation | beComap',
  description: 'Access valuable resources for indoor navigation, enhancing your understanding of this innovative technology',
  images: ["/becomap.png"],
  openGraph: {
    title: 'Resources Indoor Navigation | beComap',
  description: 'Access valuable resources for indoor navigation, enhancing your understanding of this innovative technology',
    images: ["/becomap.png"],
  },
  alternates: {
    canonical: '/blog/ ',
  }
} 


const Blog = () => {
  return (
    <div className="blog-page-wrap">
      <BlogList/>
      <div>
        <TalkToOurTeam source={"blog"} />
      </div>
    </div>
  );
};

export default Blog;
