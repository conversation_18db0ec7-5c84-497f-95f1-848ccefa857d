
import TalkToOurTeam from "@/app/components/talktoourteam";
import BlogList from "./_components/blogList/blogList";



export const metadata = {
  metadataBase: new URL('https://becomap.com'),
  title: 'Resources Indoor Navigation | beComap',
  description: 'Access valuable resources for indoor navigation, enhancing your understanding of this innovative technology',
  images: ["/becomap.png"],
  openGraph: {
    title: 'Resources Indoor Navigation | beComap',
  description: 'Access valuable resources for indoor navigation, enhancing your understanding of this innovative technology',
    images: ["/becomap.png"],
  },
  alternates: {
    canonical: '/blog/ ',
  }
} 


const Blog = () => {
  return (
    <div className="blog-page-wrap">
      <BlogList/>
      <div>
        <TalkToOurTeam />
      </div>
    </div>
  );
};

export default Blog;
