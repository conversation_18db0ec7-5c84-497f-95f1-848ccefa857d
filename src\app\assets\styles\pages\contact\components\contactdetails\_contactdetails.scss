.contact-number-box {
  background-color: $primary-color;
  background-image: url("/images/contact-left-bg.png");
  background-position: 10px 10px;
  padding: 29px 32px;
  background-repeat: no-repeat;
  border-radius: 30px;

  @media (max-width: 832px) {
    width: 100%;
  }

  @media (max-width: 1015px) {
    display: flex;
    .contact-numberbox-img {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      @media (max-width: 832px) {
        display: none;
      }
    }
  }
}
.contact-connecting-box {
  background: #ffffff;
  box-shadow: 0px 20px 100px rgba(0, 0, 0, 0.33);
  border-radius: 30px;
  @media (max-width: 1015px) {
      width: 50%;
  }
  @media (max-width: 832px) {
    width: 100%;
  }
  h3 {
    padding: 10px 38px;
    font-size: 20px;
    font-family: $font-medium;
    border-bottom: 1px solid rgba(217, 217, 217, 1);
    line-height: 20px;
    height: 54px;
    align-items: center;
    display: flex;
    margin: 0;
  }
  .contact-connectingbox-body {
    padding: 20px 38px 10px;
    @media (max-width: 1015px) {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: start;
        justify-content: start;
    }
  }
}
.contact-connectingbox-left {
  font-family: $font-medium;
  min-width: 140px;
}
.contact-connectingbox-right {
  flex: 1;
  @media (max-width: 1015px) {
    width: 100%;
  }
  span {
    background: $primary-gradient;
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 19px;
    color: #fff;
    font-family: $font-medium;
    border-radius: 30px;

    @media (max-width: 1199px) and (min-width: 1015px) {
      width: 100% !important;
    }
    @media (max-width: 991px) {
      width: 100% !important;
    }
  }
}
.contact-connectingbox-row {
  margin-bottom: 10px;
  @media (max-width: 1015px) {
    display: flex;
    // flex-direction: column;
    align-items: start!important;
    width: 100%;
  }

  @media (max-width: 832px) {
    flex-direction: column;
  }
}
@media (max-width: 1400px) {
  .contact-connectingbox-left {
    min-width: 130px;
  }
  .contact-connecting-box {
    h3 {
      padding: 10px 20px;
    }
    .contact-connectingbox-body {
      padding: 20px 20px 10px;
    }
  }
}
