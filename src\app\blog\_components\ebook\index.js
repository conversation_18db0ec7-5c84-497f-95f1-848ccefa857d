import Image from "next/image";
import Button from "react-bootstrap/Button";



const BlogEbook = () => {
  return (
    <div className="d-flex h-100">
      <div className="ebook-box-wrap">
        <div className="ebook-img">
          <Image src="/images/ebook.png" alt="ebook" width="200" height="300" />
        </div>
        <h3>
        Maximizing Beacon ROI:
          <span>Strategies for Successful Implementation and Deployment</span>
        </h3>
        <p>Maximizing ROI through effective Beacon deployment and implementation is crucial for businesses. By strategically placing Beacons, businesses can enhance customer engagement, drive foot traffic, and increase sales. Beacons enable businesses to deliver timely, relevant content to customers</p>
        <div className="btn-wrap">
          <Button className="btn-download-ebook btn-secondary w-100">
            Download Ebook
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BlogEbook;
