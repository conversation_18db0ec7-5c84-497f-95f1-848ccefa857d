.blog-card {
  background: #ffffff;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.17);
  border-radius: 22px;
  padding: 20px;
  border: none;
  .card-body {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: stretch;
  }
  .card-title {
    font-size: 20px;
    line-height: 26px;
    font-family: $font-bold;
    margin-bottom: 15px;
    min-height: 50px;
  }
  .card-text {
    line-height: 24px;
    min-height: 72px;
  }
}
.blog-img-wrap {
  img {
    border-radius: 16px;
  }
}
.blogslider {
  .blog-card-wrap {
    @media (min-width: 767px) {
      max-width: 380px;
    }
  }
  .swiper-slide {
    padding: 15px;
    @media (min-width: 767px) {
      max-width: 380px;
    }
  }
  .border-btn {
    border: 1px solid #adaaaa;
    background-color: #fff;
    color: #000;
  }
}
.writer-mention {
  font-size: 13px;
  font-family: $font-bold;
  text-transform: uppercase;
  color: #5e6568;
  margin-bottom: 10px;
  line-height: 30px;
}

@media (max-width: 767px) {
  .blog-card {
    .card-title {
      min-height: inherit;
    }
  }
}
