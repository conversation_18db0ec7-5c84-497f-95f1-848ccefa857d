.casestudy-content {
    h3 {
        font-size: 30px;
        font-family: $font-bold;
        line-height: 50px;
    }
    p {
        font-size: 16px;
        line-height: 30px;
    }
    .btn {
        min-width: 189px;
    }
    .rating {
        margin-left: 35px;
    }
}
.case-study-right {
    width: 50%;
}
.case-study-left {
    width: 50%;
}
.casestudy-cell-2 {
    background: rgba(243, 243, 243, 1);
    border-radius: 32px;
    padding: 61px 57px;
    max-width: 566px;
    position: relative;
    &::after {
        content: "";
        height: 113px;
        position: absolute;
        z-index: 5;
        background: rgb(243, 243, 243);
        width: 60px;
        top: 15%;
        left: -37px;
        border-radius: 30px;
    }
    &::before {
        content: "";
        height: 113px;
        position: absolute;
        z-index: 5;
        background: rgb(243, 243, 243);
        width: 60px;
        bottom: 15%;
        left: -37px;
        border-radius: 30px;
    }
}
.case-study-section {
    padding-top: 85px;
    padding-bottom: 75px;
}
.case-study-section:nth-child(odd) {
    background: rgba(243, 243, 243, 1);
    .casestudy-cell-2 {
        background-color: #fff;
        &::after {
            background: #fff;
        }
        &::before {
            background: #fff;
        }
    }
}

.casestudy-img-wrap {
    width: 544px;
    height: 544px;
    background: rgba(52, 168, 83, 0.34);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 10;
    &::before {
        position: absolute;
        left: 39px;
        top: 39px;
        right: 39px;
        bottom: 39px;
        content: "";
        background: rgba(52, 168, 83, 1);
        border-radius: 50%;
    }
    &::after {
        position: absolute;
        left: -39px;
        top: -39px;
        right: -39px;
        bottom: -39px;
        content: "";
        background: rgba(52, 168, 83, 0.34);
        border-radius: 50%;
    }
    img {
        position: relative;
        z-index: 11;
        max-width: 100%;
        height: auto !important;
        max-height: 100%;
        width: auto !important;
    }
}
.case-study-section:nth-child(odd) {
    .casestudy-img-wrap {

        background: rgba(255, 184, 45, 0.34);

        &::before {
            background:rgba(255, 184, 45, 1);

        }
        &::after {
            background: rgba(255, 184, 45, 0.54);

        }

    }   
}

@media (max-width: 1600px) {
    .casestudy-img-wrap {
        width: 384px;
        height: 384px;
    }
}
@media (max-width: 1200px) {
    .casestudy-cell-2 {
        padding: 40px 22px;
    }
    .casestudy-content .rating {
        margin-left: 15px;
    }
    .casestudy-img-wrap {
        width: 284px;
        height: 284px;
    }
    .case-study-left {
        width: 55%;
    }
    .case-study-right {
        width: 45%;
    }
    .case-study-section {
        .container {
            max-width: 90%;
        }
    }
}
@media (max-width: 991px) {
    .casestudy-img-wrap::after {
        left: -19px;
        top: -19px;
        right: -19px;
        bottom: -19px;
    }
    .casestudy-img-wrap::before {
        left: 19px;
        top: 19px;
        right: 19px;
        bottom: 19px;
    }
    .casestudy-footer {
        flex-direction: column;
        .btn {
            width: 100%;
            margin-bottom: 15px;
        }
    }
    .casestudy-cell-2 {
        &::after {
            width: 40px;
            left: -25px;
            border-radius: 20px;
        }
        &::before {
            width: 40px;
            left: -25px;
            border-radius: 20px;
        }
    }
    .casestudy-content {
        h3 {
            font-size: 22px;
            line-height: 30px;
        }
        p {
            font-size: 14px;
            line-height: 26px;
            margin-bottom: 20px !important;
        }
        .rating {
            margin-left: 0;
        }
    }
}
@media (max-width: 767px) {
    .case-study-left {
        width: 100%;
        justify-content: center;
        order: 2;
    }
    .case-study-right {
        width: 100%;
        justify-content: center !important;
        order: 1;
        margin-bottom: 50px;
    }
    .casestudy-cell-2 {
        max-width: 100%;
    }

    .case-study-section {
        padding-top: 35px;
        padding-bottom: 25px;
    }
    .casestudy-cell-2::before {
        display: none;
    }
    .casestudy-cell-2::after {
        display: none;
    }
    .casestudy-bannner-bg {
        height: 300px;
        object-fit: cover;
        img {
            height: 100% !important;
        }
    }
}
