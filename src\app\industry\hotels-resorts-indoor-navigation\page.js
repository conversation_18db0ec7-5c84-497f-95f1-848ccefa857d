import ClientSlider from "@/app/company/_components/companybanner/components/clients";
import IndustryBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
import IndustryExperience from "../_components/IndustryExperience";
import IndustrySolutions from "../_components/solutions";
import IndustryAccordion from "../_components/IndustryAccordion";
import IndustryServices from "../_components/IndustryServices";
import IndustryEBook from "../_components/IndustryEBook";
import IndustryHelp from "../_components/IndustryHelp";
import HeroImg from "@/app/assets/images/industry/hotel-hero.png";
import SecImg from "@/app/assets/images/industry/health-sec-im.png";
import SecImg2 from "@/app/assets/images/industry/health-sec-im-2.png";
import SecImg3 from "@/app/assets/images/industry/event-help-illu.png";
import TalkToOurTeam from "@/app/components/talktoourteam";
import BgImg from "@/app/assets/images/industry/hotel-accordion-bg.png";
import FaqSection from "../_components/FaqSection";
import { Col, Container, Row } from "react-bootstrap";
import Image from "next/image";

export const metadata = {
  metadataBase: new URL("https://becomap.com"),
  title: "Indoor Navigation for Hotels and Resorts | Becomap",
  description:
    "Transform guest experiences with Becomap's indoor navigation for hotels and resorts. Simplify navigation and  improve engagement.",
  images: ["/becomap.png"],
  openGraph: {
    title: "Indoor Navigation for Hotels and Resorts | Becomap",
    description:
      "Transform guest experiences with Becomap's indoor navigation for hotels and resorts. Simplify navigation and  improve engagement.",
    images: ["/becomap.png"],
  },
};

const mallNavigationFAQ = [
  {
    question: "Can Becomap’s solution adapt to my property’s unique layout?",
    answer:
      "Yes! Becomap customizes navigation paths based on your specific layout, ensuring guests have the most efficient routes for your unique space.",
  },
  {
    question: "What data can hotel management access for operational insights?",
    answer:
      "Becomap offers data insights into guest movement patterns, helping you manage foot traffic, optimize spaces, and make data-driven layout decisions.",
  },
  {
    question: "How are guests informed of on-site events and activities?",
    answer:
      "Guests receive updates on special events, live entertainment, and scheduled activities, enriching their experience and increasing engagement.",
  },
 {
    question: "How can a navigation solution encourage repeat visits?",
    answer:
      "By providing guests with an easy, enjoyable experience, guests are more likely to leave positive reviews and return in the future.",
  },

];


const SOLUTIONS = [
  {
    title: "Clear Directions",
    description:
      "beComap’s Indoor navigation offers clear, easy-to-follow directions for patients and visitors to find their way through the hospital. From rooms to pharmacies, visitors can navigate smoothly, reducing confusion and saving time.",
    iconUrl: "/images/icons/patient.png",
    color: "#00BEDA",
  },
  {
    title: "Make Essential Services Easy to Find",
    description:
      "In a busy hospital, services like emergency rooms, labs, and pharmacies are crucial. beComap lets you highlight these key areas so patients can reach them without delay. No more guessing, just clear directions to the care they need.",
    iconUrl: "/images/icons/coins.png",
    color: "#ED55B9",
  },
  {
    title: "Use Data to Improve Efficiency",
    description:
      "Understanding how patients and visitors move through your hospital can help you optimize operations. beComap’s data insights allow you to monitor foot traffic, improve your layout, and keep things running smoothly—all without intruding on user privacy.",
    iconUrl: "/images/icons/management.png",
    color: "#ED790F",
  },
];

const accordionData = [
  {
    title: "Multi-Language Support",
    content:
      "Offer written directions in multiple languages to accommodate international guests. beComap makes it easier for everyone to explore your hotel or resort, creating a more inclusive and user-friendly experience.",
  },
  {
    title: "Instant Alerts",
    content:
      "Keep guests informed about any important updates like event changes or service availability with real-time notifications. beComap allows you to quickly communicate with your guests, ensuring they stay informed throughout their stay.",
  },
  {
    title: "Less Searching, More Relaxing",
    content:
      "beComap helps guests easily find their way to pools, restaurants, and other amenities, so they can spend more time enjoying their stay instead of searching for services around the property.",
  },
  {
    title: "Stress-Free Navigation",
    content:
      "Large hotels and resorts can be overwhelming, especially for new guests. beComap provides step-by-step directions, ensuring guests can navigate confidently and make the most of their visit, whether they’re first-timers or repeat visitors.",
  },
  {
    title: "No App Downloads Required",
    content:
      "Guests can scan a QR code to instantly access beComap’s navigation system through their web browser—no downloads or additional apps needed, making navigation simple and convenient.",
  },
  {
    title: "Data-Driven Hotel Management",
    content:
      "Managing a large hotel or resort involves directing foot traffic and ensuring guests access services easily. beComap provides insights into guest movement, helping optimize space, improve layout efficiency, and enhance the overall guest experience. Streamline operations with data-driven decisions using beComap.",
  },
];

const ServiceData = [
  {
    title: "QR Code Scanning. No Downloads Needed",
    content:
      "beComap’s web-based solution means there are no app downloads required. Patients and visitors simply scan a QR code at the entrance to access step-by-step directions, making navigation effortless without additional hassle.",
    icon: "/images/icons/rfid.png",
    image: "/images/qr-code-scanning-health.png",
  },
  {
    title: "Parking Made Simple",
    content:
      "Finding a parking spot shouldn’t be another headache for visitors. beComap organizes hospital parking into zones with QR codes. Visitors scan the code when they park and are guided back to their vehicle when it’s time to leave, saving them from wandering around the lot.",
    icon: "/images/icons/parked-car.png",
    image: "/images/parking-made-simple.png",
  },
  {
    title: "Optimize Hospital Operations",
    content:
      "Managing a hospital involves a lot of moving parts. beComap helps you track equipment, manage space, and assign tasks efficiently, all from one simple dashboard. You’ll have everything you need to make your hospital run like a well-organized system, keeping both patients and staff happy.",
    icon: "/images/icons/pharmacy.png",
    image: "/images/optimize-hospital-operations.png",
  },
  {
    title: "Tailored Maps for Your Hospital’s Branding",
    content:
      "Customize your hospital’s map to reflect its branding with beComap’s flexible design options. From logos to color schemes, beComap ensures your hospital map is visually consistent with your identity while enhancing the patient experience.",
    icon: "/images/icons/map01.png",
    image: "/images/tailored-maps.png",
  },
];

// const [lineWidth, setLineWidth] = useState(1204);

// useEffect(() => {
//   const updateBgWidth = () => {
//     if (animRef.current) {

//       bgRef.current.style.width = `${distanceFromLeft + 150}px`;
//       const FloatingWidth =
//         routeType === "hospital"
//           ? 224
//           : routeType === "shoppingmall"
//           ? 111
//           : 0;
//       animRef.current.style.left = `${distanceFromRight - FloatingWidth}px`;

//       const updatedLineWidth = animRef.current.getBoundingClientRect().width;
//       setLineWidth(updatedLineWidth);
//     }
//   };

//   updateBgWidth();

//   window.addEventListener("resize", updateBgWidth);

//   return () => {
//     window.removeEventListener("resize", updateBgWidth);
//   };
// }, []);

const Hotels = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <IndustryBanner
        source="hotels-resorts"
        bgImage={HeroImg}
        title={`Indoor Navigation for Hotels & Resorts `}
        description="Transform your hotel or resort into a guest-friendly destination where visitors can easily navigate the property, discover amenities, and maximize their experience."
      />
      <IndustryClients />
      <section
        className="section bg-bc-light "
        style={{ position: "relative" }}
      >
        <div className="company-value-head mb-2">
          <h2 className="main-title">Clear Paths, Happier Guests</h2>
          <p>
            {`Navigating large hotel properties or sprawling resorts can be confusing for guests, especially if they are unfamiliar with the layout. beComap’s Indoor Navigation provides easy-to-follow, step-by-step directions, ensuring that your guests spend more time relaxing and less time trying to figure out where they are. From check-in to discovering on-site restaurants, pools, or spas, guiding them to the rooms, services, and recreational activities they’re most interested in. beComap helps guests find everything they need, creating a more enjoyable and hassle-free stay.`}
          </p>
        </div>
        {/* <div
          ref={animRef}
          // className="bg-anim-ort"
        >
          <svg
            width="auto"
            height="100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2 5H739C747.284 5 754 11.7157 754 20V80C754 88.2843 760.716 95 769 95H1147C1155.28 95 1162 88.2843 1162 80V20C1162 11.7157 1168.72 5 1177 5H1920"
              stroke="white"
              strokeWidth="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div> */}
        <div className="event-sec">
          <Container fluid={"xl"}>
            <Row>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#00BEDA" }}
                    >
                      <Image
                        src={"/images/icons/location-1.png"}
                        alt={"Amenity Discovery"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">Amenity Discovery</h4>
                  </div>
                  <p className="">
                    beComap helps guests locate important amenities like
                    restaurants, pools, and fitness centers, ensuring they make
                    the most of their stay.
                  </p>
                </div>
              </Col>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5 mt-lg-5 mt-4">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#ED55B9" }}
                    >
                      <Image
                        src={"/images/icons/event.png"}
                        alt={"Event Awareness"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">Event Awareness</h4>
                  </div>
                  {/* eslint-disable react/no-unescaped-entities */}
                  <p className="mb-0">
                    Guests are informed of special events, live entertainment,
                    and scheduled activities, helping them engage more deeply
                    with the resort experience.
                  </p>
                </div>
              </Col>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5 mt-lg-0 mt-4">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#ED790F" }}
                    >
                      <Image
                        src={"/images/icons/map-2.png"}
                        alt={"Clear Navigation"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">Comfortable Exploration</h4>
                  </div>
                  <p className="mb-0">
                    beComap provides detailed information on accessible routes,
                    elevators, and facilities, making it easier for all guests
                    to navigate the property.
                  </p>
                </div>
              </Col>
              {/* eslint-enable react/no-unescaped-entities */}
            </Row>
          </Container>
        </div>
        {/* <svg
          width="auto"
          height="74"
          // viewBox="0 0 1920 74"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1941 5H1240.14C1231.85 5 1225.14 11.7157 1225.14 20V54C1225.14 62.2843 1218.42 69 1210.14 69H-21"
            stroke="white"
            strokeOpacity="0.5"
            strokeWidth="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg> */}
      </section>
      <IndustryAccordion
        title={"Improving Guest Experience and Property Efficiency"}
        description={
          "Making it easy for guests to navigate and find amenities like restaurants, pools, and event spaces greatly enhances their stay. beComap ensures guests can explore your property effortlessly, leading to higher satisfaction and increasing the chances of repeat visits.At the same time, beComap provides management with data on how guests move through the property. This information allows you to adjust the layout and better manage foot traffic, ensuring a smoother experience for both guests and staff. The result is a property that operates more efficiently while delivering a better overall guest experience."
        }
        bgImage={BgImg}
        accordionData={accordionData}
        videoSrc={"/videos/health.mp4"}
      />
      <IndustryHelp
        title="Why Leading Hotels & Resorts Trust beComap"
        description="beComap changes the way you manage hotel navigation and guest engagement. Our platform simplifies navigation, encourages return visits, and optimizes hotel operations to improve the overall guest journey."
        image={SecImg3}
        points={[
          "Trusted by world-class hotels and resorts for indoor navigation",
          "8 years of experience in location technology",
          "Optimized for mobile devices, web browsers, and kiosks",
          "Easy integration with existing hotel apps",
          "Web-based navigation with no downloads required",
          "Multi-language support for international guests",
        ]}
      />

      <IndustryEBook
        image={"/images/event-ebook-bg.png"}
        title={"Logistics In Action"}
        description={`<p>Unlock indoor navigation potential with our eBook. </p> <p>Explore technologies, strategies, and best practices to enhance wayfinding, visitor experience, and operations.</p>`}
      />

      <TalkToOurTeam source={"hotels-resorts"}/>
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={mallNavigationFAQ}
      />
    </div>
  );
};

export default Hotels;
