import axios from "axios";
import { API_BASE_URL, API_ENDPOINTS, API_HEADERS } from "../constants";

export async function formSubmit(formData, contactIdentifier) {
  try {
    const response = await axios.post(
      `${API_BASE_URL}${API_ENDPOINTS.CONTACT}`,
      formData,
      {
        headers: {
          "Content-Type": API_HEADERS.CONTENT_TYPE,
          "X-Contact-Identifier": contactIdentifier,
        },
      }
    );
    return response.status;
  } catch (error) {
    console.error("Error submitting form:", error);
    return error.response ? error.response.status : 500;
  }
}

export async function downloadFormSubmit(formData) {
  try {
    const response = await axios.post(
      `${API_BASE_URL}${API_ENDPOINTS.DOWNLOADS}`,
      formData,
      {
        headers: {
          "Content-Type": API_HEADERS.CONTENT_TYPE,
        },
      }
    );
    return response.status;
  } catch (error) {
    console.error("Error submitting form:", error);
    return error.response ? error.response.status : 500;
  }
}

export async function subscribeFormSubmit(formData) {
  try {
    const response = await axios.post(
      `${API_BASE_URL}${API_ENDPOINTS.SUBSCRIBE}`,
      formData,
      {
        headers: {
          "Content-Type": API_HEADERS.CONTENT_TYPE,
        },
      }
    );
    return response;
  } catch (error) {
    console.error("Error submitting form:", error);
    return {
      status: error.response ? error.response.status : 500,
      data: error.response
        ? error.response.data
        : { message: "Form submission failed" },
    };
  }
}

export async function getCarouselBlog(page, pageSize) {
  try {
    const response = await axios.get(`${API_BASE_URL}${API_ENDPOINTS.POSTS}`, {
      headers: {
        "X-Blog-Identifier": API_HEADERS.BLOG_IDENTIFIER,
      },
      params: {
        page,
        page_size: pageSize,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching blog posts:", error);
    return error.response ? error.response.status : 500;
  }
}

export async function getBlogSearch(searchTerm) {
  try {
    const response = await axios.get(`${API_BASE_URL}${API_ENDPOINTS.POSTS}`, {
      headers: {
        "X-Blog-Identifier": API_HEADERS.BLOG_IDENTIFIER,
      },
      params: {
        search: searchTerm,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error searching blog posts:", error);
    return error.response ? error.response.status : 500;
  }
}
