"use client";
import { <PERSON>, Button } from "react-bootstrap";
import { subscribFormSubmit } from "@/app/action/action";
import { Formik } from "formik";
import { useState } from "react";

function FooterSignup() {
  const [isSuccess, setIsSuccess] = useState(false);
  const [fieldError, setFieldError] = useState(null); // Corrected here

  return (
    <div className="footer-signup-wrap mb-5">
      <h6 className="mb-30">
        Sign up for the latest industry updates, resources, and news
      </h6>
      <Formik
        initialValues={{ email: "" }}
        validate={(values) => {
          const errors = {};
          if (!values.email) errors.email = "Email is Required";
          else if (
            !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)
          ) {
            errors.email = "Invalid email address";
          }
          return errors;
        }}
        onSubmit={async (values, { setSubmitting, resetForm }) => {
          try {
            const result = await subscribFormSubmit(values);
            const { status, data } = result;
            if (status === 201) {
              setIsSuccess(true);
              setSubmitting(false);
              setTimeout(() => {
                resetForm();
                setIsSuccess(false);
              }, 5000);
            } else if (status === 400 && data.email[0].includes("already exists")) {
              setFieldError("This email is already subscribed.");
              setTimeout(() => setFieldError(null), 5000);
            } else {
              console.error(`Form submission failed with status: ${status}`);
            }
          } catch (error) {
            console.error(`Form submission failed: ${error.message}`);
          }
        }}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          isSubmitting,
        }) => (
          <Form
            className={`d-flex align-items-center ft-signup-form ${
              isSuccess ? "success" : ""
            }`}
            onSubmit={handleSubmit}
          >
            <Form.Control
              type="email"
              name="email"
              placeholder="Email*"
              value={values.email}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={touched.email && !!errors.email}
              style={{
                height: "60px",
                borderRadius: "100px",
                paddingRight: "124px",
                paddingLeft: "26px",
              }}
            />
            <span
              className={`pop-error ${
                (touched.email && !!errors.email) || fieldError ? "active" : ""
              }`}
            >
              {errors.email || fieldError}
            </span>
            <Button
              variant="primary"
              type="submit"
              disabled={isSubmitting}
              style={{ marginLeft: "-120px" }}
            >
              {isSubmitting
                ? "Signing Up..."
                : isSuccess
                ? "Success!"
                : "Sign Up"}
            </Button>
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default FooterSignup;
