import React from 'react';
import { Row, Col, Container } from 'react-bootstrap';
import Button from 'react-bootstrap/Button';
import Image from 'next/image';
import ClientSlider from './components/clients';

const CompanyBanner = () => {
    return (
        <section className='company-banner-wrap'>
            <Container fluid={"lg"}>
                <Row className='align-items-center p-relative cmp-row'>
                    <Col md={6}>
                        <div className='company-banner-image d-flex align-items-end'>
                            <Image src='/images/company-banner-img.png' alt='banner-image' width="500" height="538" style={{ width: '100%', height:'auto' }} />
                        </div>
                    </Col>
                    <Col md={6}>
                        <div className='company-banner-details-right'>
                            <div className='company-banner-details mb-lg-5 mb-md-4 mb-0 mt-5 mt-md-0'>
                                <h2 className='main-title mb-3'>Partner With Us To Grow Your Business</h2>
                                <p>beCo is a team driven by a passion for simplifying indoor navigation. Led by a former Apple Engineering Head, we combine over 15 years of experience with a mission to make indoor spaces easier to navigate, optimize operations for businesses across industries.</p>
                                <div className='btn-wrap'>
                                    <Button className="btn-secondary">Let&apos;s talk about your project now</Button>
                                </div>
                            </div>
                            <div className='company-banneslider'>
                                <h4>Trusted By <span>Over 230 happy clients. 60+ projects delivered</span></h4>
                                <ClientSlider />
                            </div>
                        </div>
                    </Col>
                </Row>
            </Container>
        </section>
    );
};

export default CompanyBanner;
