import ClientSlider from "@/app/company/_components/companybanner/components/clients";
import IndustryBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
import IndustryExperience from "../_components/IndustryExperience";
import IndustrySolutions from "../_components/solutions";
import IndustryAccordion from "../_components/IndustryAccordion";
import IndustryServices from "../_components/IndustryServices";
import IndustryEBook from "../_components/IndustryEBook";
import IndustryHelp from "../_components/IndustryHelp";
import HeroImg from "@/app/assets/images/industry/school-hero.png";
import SecImg from "@/app/assets/images/industry/health-sec-im.png";
import SecImg2 from "@/app/assets/images/industry/health-sec-im-2.png";
import SecImg3 from "@/app/assets/images/industry/school-help-illu.png";
import TalkToOurTeam from "@/app/components/talktoourteam";
import BgImg from "@/app/assets/images/industry/school-accordion-bg.png";
import FaqSection from "../_components/FaqSection";
import { Col, Container, Row } from "react-bootstrap";
import Image from "next/image";

export const metadata = {
  metadataBase: new URL("https://becomap.com"),
  title: "Indoor Navigation System for Schools and Universities | Becomap",
  description:
    "Improve navigation for schools and universities with Becomap's indoor navigation system, helping students and visitors easily locate key areas.",
  images: ["/becomap.png"],
  openGraph: {
    title: "Indoor Navigation System for Schools and Universities | Becomap",
    description:
      "Improve navigation for schools and universities with Becomap's indoor navigation system, helping students and visitors easily locate key areas.",
    images: ["/becomap.png"],
  },
};

const eventNavigationFAQ = [
  {
    question: "How can indoor navigation help new students and visitors on campus?",
    answer: "The system provides step-by-step directions to classrooms, lecture halls, libraries, and other facilities, ensuring new students and visitors can navigate the campus easily."
  },
  {
    question: "Does the system support multi-building and multi-floor campuses?",
    answer: "Yes, the system is designed to handle complex layouts, including multiple buildings and floors, with clear navigation and floor-switching features."
  },
  {
    question: "Can the navigation system provide information about campus events and schedules?",
    answer: "Absolutely. The system can integrate event schedules, announcements, and notifications to keep users informed in real time."
  },
  {
    question: "Is the system accessible on both smartphones and tablets?",
    answer: "Yes, the navigation system is compatible with smartphones, tablets, and other devices, ensuring accessibility for all users."
  }
];

const SOLUTIONS = [
  {
    title: "Clear Directions",
    description:
      "beComap’s Indoor navigation offers clear, easy-to-follow directions for patients and visitors to find their way through the hospital. From rooms to pharmacies, visitors can navigate smoothly, reducing confusion and saving time.",
    iconUrl: "/images/icons/patient.png",
    color: "#00BEDA",
  },
  {
    title: "Make Essential Services Easy to Find",
    description:
      "In a busy hospital, services like emergency rooms, labs, and pharmacies are crucial. beComap lets you highlight these key areas so patients can reach them without delay. No more guessing, just clear directions to the care they need.",
    iconUrl: "/images/icons/coins.png",
    color: "#ED55B9",
  },
  {
    title: "Use Data to Improve Efficiency",
    description:
      "Understanding how patients and visitors move through your hospital can help you optimize operations. beComap’s data insights allow you to monitor foot traffic, improve your layout, and keep things running smoothly—all without intruding on user privacy.",
    iconUrl: "/images/icons/management.png",
    color: "#ED790F",
  },
];

const accordionData = [
  {
    title: "Easily Integrate Maps",
    content:
      "Integrate your campus map into existing apps to guide students and staff to their classrooms, dormitories, and services via the shortest routes, ensuring they always arrive on time.",
  },
  {
    title: "Multi-Language Support",
    content:
      "Make your campus map accessible to all visitors by offering it in multiple languages. beComap allows you to provide directions and information in the preferred language of the user, making the experience more inclusive and user-friendly—especially helpful in diverse or international campuses.",
  },
  {
    title: "Stay Updated with Campus Alerts",
    content:
      "Keep students and staff informed with instant updates on campus events, closures, or other important alerts. Our system allows you to deliver timely notifications to improve communication and keep everyone on the same page.",
  },
  {
    title: "Less Searching, More Learning",
    content:
      "Navigate your campus in a flash—find the fastest route from dorms to classrooms, or plan a multi-stop trip to cover multiple locations. With our system, students and staff spend less time searching and more time engaging in campus activities.",
  },
  {
    title: "Easy Integration with Existing Systems",
    content:
      "Schools often hesitate to adopt new technology due to setup and compatibility concerns. beComap removes this barrier with a navigation system that works alongside your school’s app or as a standalone tool. It fits in easily with your existing systems, making campus navigation accessible to everyone, whether they’re on a mobile device, a web browser, or using a kiosk.",
  },
  {
    title: "Stress-Free Parking for Students and Staff",
    content:
      "Finding parking on a busy campus is challenging, and remembering your spot adds to the stress. With beComap’s parking assistance, large lots are divided into zones with QR codes in each. Students and staff can scan the code when they park, allowing our web app to guide them back to their vehicle later. This feature eliminates the hassle of searching in crowded lots, making campus visits more convenient and efficient.",
  },
];

const ServiceData = [
  {
    title: "QR Code Scanning. No Downloads Needed",
    content:
      "beComap’s web-based solution means there are no app downloads required. Patients and visitors simply scan a QR code at the entrance to access step-by-step directions, making navigation effortless without additional hassle.",
    icon: "/images/icons/rfid.png",
    image: "/images/qr-code-scanning-health.png",
  },
  {
    title: "Parking Made Simple",
    content:
      "Finding a parking spot shouldn’t be another headache for visitors. beComap organizes hospital parking into zones with QR codes. Visitors scan the code when they park and are guided back to their vehicle when it’s time to leave, saving them from wandering around the lot.",
    icon: "/images/icons/parked-car.png",
    image: "/images/parking-made-simple.png",
  },
  {
    title: "Optimize Hospital Operations",
    content:
      "Managing a hospital involves a lot of moving parts. beComap helps you track equipment, manage space, and assign tasks efficiently, all from one simple dashboard. You’ll have everything you need to make your hospital run like a well-organized system, keeping both patients and staff happy.",
    icon: "/images/icons/pharmacy.png",
    image: "/images/optimize-hospital-operations.png",
  },
  {
    title: "Tailored Maps for Your Hospital’s Branding",
    content:
      "Customize your hospital’s map to reflect its branding with beComap’s flexible design options. From logos to color schemes, beComap ensures your hospital map is visually consistent with your identity while enhancing the patient experience.",
    icon: "/images/icons/map01.png",
    image: "/images/tailored-maps.png",
  },
];

// const [lineWidth, setLineWidth] = useState(1204);

// useEffect(() => {
//   const updateBgWidth = () => {
//     if (animRef.current) {

//       bgRef.current.style.width = `${distanceFromLeft + 150}px`;
//       const FloatingWidth =
//         routeType === "hospital"
//           ? 224
//           : routeType === "shoppingmall"
//           ? 111
//           : 0;
//       animRef.current.style.left = `${distanceFromRight - FloatingWidth}px`;

//       const updatedLineWidth = animRef.current.getBoundingClientRect().width;
//       setLineWidth(updatedLineWidth);
//     }
//   };

//   updateBgWidth();

//   window.addEventListener("resize", updateBgWidth);

//   return () => {
//     window.removeEventListener("resize", updateBgWidth);
//   };
// }, []);

const SchoolsUniversities = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <IndustryBanner
        source={"schools-universities"}
        bgImage={HeroImg}
        title={`Campus Indoor Navigation Made Easy for Schools & Universities`}
        description="Turn your school into an accessible, well-organized space where everyone can find their way and stay connected."
      />
      <IndustryClients />
      <section
        className="section bg-bc-light "
        style={{ position: "relative" }}
      >
        <div className="company-value-head mb-2">
          <h2 className="main-title">
            Clear Paths, Smarter Campuses Experience
          </h2>
          <p>
            {`Lost on campus? We’ve all been there! Students rushing to class, visitors trying to find the right building, or staff needing to reach a department quickly. Navigating a large campus shouldn’t be a challenge. beComap’s Indoor Navigation solves this by providing clear, step-by-step directions across the campus, guiding everyone to the right classrooms, offices, and campus services. Our system keeps the campus organized, informed, and easy to navigate, creating a smoother experience for all.`}
          </p>
        </div>
        {/* <div
          ref={animRef}
          // className="bg-anim-ort"
        >
          <svg
            width="auto"
            height="100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2 5H739C747.284 5 754 11.7157 754 20V80C754 88.2843 760.716 95 769 95H1147C1155.28 95 1162 88.2843 1162 80V20C1162 11.7157 1168.72 5 1177 5H1920"
              stroke="white"
              strokeWidth="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div> */}
        <div className="event-sec">
          <Container fluid={"xl"}>
            <Row>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#00BEDA" }}
                    >
                      <Image
                        src={"/images/icons/event.png"}
                        alt={"Clear Navigation"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">
                      Spotlight Important Campus Services and Events
                    </h4>
                  </div>
                  <p className="">
                    beComap helps students and staff discover important services
                    and key events happening on campus. From dining halls to
                    seminars, users can find what’s important to them, turning
                    each visit into an opportunity for better engagement with
                    campus life.
                  </p>
                </div>
              </Col>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5 mt-lg-5 mt-4">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#ED55B9" }}
                    >
                      <Image
                        src={"/images/icons/location-1.png"}
                        alt={"Clear Navigation"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">
                      Data-Driven Campus Management
                    </h4>
                  </div>
                  {/* eslint-disable react/no-unescaped-entities */}
                  <p className="mb-0">
                    Track how students and staff move through your campus to
                    make informed decisions about room assignments, event
                    planning, and space utilization. With data insights from
                    beComap, you can optimize your campus layout, improve
                    operations, and enhance the overall campus experience—all
                    while respecting user privacy.
                  </p>
                </div>
              </Col>
              <Col lg="4">
                <div className="solution-item-box h-auto py-5 mt-lg-0 mt-4">
                  <div className="solution-icon-wrap d-flex flex-column align-items-start">
                    <span
                      className="srv-icon-bg"
                      style={{ backgroundColor: "#ED790F" }}
                    >
                      <Image
                        src={"/images/icons/management.png"}
                        alt={"Clear Navigation"}
                        width={28}
                        height={28}
                        className="service-icon-img"
                      />
                    </span>
                    <h4 className="small-head">
                      Unlock Campus Resources Effortlessly
                    </h4>
                  </div>
                  <p className="mb-0">
                    With beComap, students and staff can easily access a full
                    list of key services, amenities, and campus facilities. From
                    library hours to department locations, our system helps
                    everyone stay informed and make the most of their
                    day—enhancing both productivity and campus engagement.
                  </p>
                </div>
              </Col>
              {/* eslint-enable react/no-unescaped-entities */}
            </Row>
          </Container>
        </div>
        {/* <svg
          width="auto"
          height="74"
          // viewBox="0 0 1920 74"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1941 5H1240.14C1231.85 5 1225.14 11.7157 1225.14 20V54C1225.14 62.2843 1218.42 69 1210.14 69H-21"
            stroke="white"
            strokeOpacity="0.5"
            strokeWidth="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg> */}
      </section>
      <IndustryAccordion
        title={"Say Goodbye to Campus Confusion"}
        description={
          "Managing a large campus requires more than navigation, it involves tracking assets, assigning tasks, and optimizing resource use. For new students and visitors, navigating a campus with numerous buildings and hallways can be confusing. beComap simplifies this by providing clear, step-by-step directions, helping everyone find their way effortlessly. From the first day to daily routines, beComap makes campus operations and navigation easy and stress-free."
        }
        bgImage={BgImg}
        accordionData={accordionData}
        videoSrc={"/videos/mall.mp4"}
      />
      <IndustryHelp
        title="Why World’s largest venues trust beComap"
        description="beComap changes the way you think about retail navigation & engagement. Our platform simplifies navigation, attracts and retains customers, and optimizes operations to improve the overall shopping journey."
        image={SecImg3}
        points={[
          "Best-in-class indoor mapping for malls",
          "8 years on active R&D in location intelligence",
          "Optimized for use on mobile devices, web browsers, and kiosks",
          "Easy integration with existing apps",
          "Web-based navigation without any downloads",
          "Multiple language support",
        ]}
      />

      <IndustryEBook
        image={"/images/event-ebook-bg.png"}
        title={"Logistics In Action"}
        description={`<p>Unlock indoor navigation potential with our eBook. </p> <p>Explore technologies, strategies, and best practices to enhance wayfinding, visitor experience, and operations.</p>`}
      />

      <TalkToOurTeam source={"schools-universities"} />
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={eventNavigationFAQ}
      />
    </div>
  );
};

export default SchoolsUniversities;
