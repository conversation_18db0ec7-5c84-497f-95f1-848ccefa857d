// .our-technology-section {
//   padding-bottom: 0px;
//   padding-top: 105px;
// }
// .technology-left {
//   width: 33%;
//   margin-left: -15px;
//   margin-top: -105px;
// }
// .technology-right {
//   padding-left: 74px;
//   padding-right: 130px;
//   .main-title {
//     margin-bottom: 37px;
//   }
// }
// .beacons-icon-bg {
//   background-color: #00beda;
// }
// .geo-fencing-icon-bg {
//   background-color: #858bf3;
// }
// .python-app-icon-bg {
//   background-color: #ed790f;
// }
// .location-data-icon-bg {
//   background-color: #ed55b9;
// }
// .frontend-icon-bg {
//   background-color: #43d200;
// }
// .technology-item-box {
//   margin-top: 23px;
//   margin-bottom: 23px;
//   padding-right: 20px;
//   p {
//     line-height: 26px;
//     margin-bottom: 0;
//   }
// }
// .our-technology-section {
//   background: linear-gradient(278.77deg, #007720 5.93%, #34ba5c 90.68%);
//   color: #fff;
// }
// .technology-icon-wrap {
//   margin-bottom: 15px;
//   .srv-icon-bg {
//     width: 54px;
//     height: 54px;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     border-radius: 50%;
//   }
//   .box-small-head {
//     margin-left: 15px;
//   }
// }
// .box-small-head {
//   margin-bottom: 0;
//   font-family: $font-medium;
//   font-size: 18px;
//   flex: 1;
// }
// .technology-img {
//   // object-fit: contain;
//   width: 100%;
//   height: auto;
// }

// @media (max-width: 1600px) {
//   .technology-right {
//     padding-left: 44px;
//     padding-right: 30px;
//   }

//   .our-technology-section {
//     padding-top: 65px;
//     padding-bottom: 45px;
//   }
//   .technology-left {
//     display: flex;
//     margin-top: 0;
//     margin-bottom: -45px;
//     align-items: flex-end;
//   }
// }
// @media (max-width: 767px) {
//   .technology-left {
//     display: none;
//   }
//   .technology-right {
//     padding-left: 15px;
//     padding-right: 10px;
//     .main-title {
//       margin-bottom: 0;
//     }
//   }
// }
.our-technology-section {
  background-image: url("/images/technology-bg.png");
  background-repeat: no-repeat;
  background-size: cover;
  .folder {
    background-image: url("/images/folder.png");
    background-repeat: no-repeat;
    background-size: contain;
    width: 464px;
    height: 100%;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    color: #000;
    position: relative;
    padding: 90px 40px 40px;
    .technology-item-box {
      padding: 0;
      .srv-icon-bg {
        width: 54px;
        height: 54px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: $secondary-color;
        position: absolute;
        top: 20px;
        right: 40px;
      }
      .box-small-head {
        font-size: 24px;
        font-weight: 600;
        text-align: start;
        line-height: 42px;
        border-bottom: 4px solid $primary-color;
        margin-bottom: 15px;
      }
      p {
        margin-bottom: 0;
      }
    }
    
  }
  .swiper-wrapper {
    .swiper-pagination {
        left: 0;
        right: auto;
        height: 100%;
        padding-top: 55px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .swiper-pagination-bullet {
            flex: 1;
            width: 6px;
            border-radius: 50px;
            background-color: #c6ffd5;

            &.swiper-pagination-bullet-active {
                background-color: $secondary-color;
            }
        }
    }
    .swiper-slide {
        margin-left: 20px;
    }
}
}
