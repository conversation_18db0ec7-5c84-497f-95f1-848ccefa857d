// .our-technology-section {
//   padding-bottom: 0px;
//   padding-top: 105px;
// }
// .technology-left {
//   width: 33%;
//   margin-left: -15px;
//   margin-top: -105px;
// }
// .technology-right {
//   padding-left: 74px;
//   padding-right: 130px;
//   .main-title {
//     margin-bottom: 37px;
//   }
// }
// .beacons-icon-bg {
//   background-color: #00beda;
// }
// .geo-fencing-icon-bg {
//   background-color: #858bf3;
// }
// .python-app-icon-bg {
//   background-color: #ed790f;
// }
// .location-data-icon-bg {
//   background-color: #ed55b9;
// }
// .frontend-icon-bg {
//   background-color: #43d200;
// }
// .technology-item-box {
//   margin-top: 23px;
//   margin-bottom: 23px;
//   padding-right: 20px;
//   p {
//     line-height: 26px;
//     margin-bottom: 0;
//   }
// }
// .our-technology-section {
//   background: linear-gradient(278.77deg, #007720 5.93%, #34ba5c 90.68%);
//   color: #fff;
// }
// .technology-icon-wrap {
//   margin-bottom: 15px;
//   .srv-icon-bg {
//     width: 54px;
//     height: 54px;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     border-radius: 50%;
//   }
//   .box-small-head {
//     margin-left: 15px;
//   }
// }
// .box-small-head {
//   margin-bottom: 0;
//   font-family: $font-medium;
//   font-size: 18px;
//   flex: 1;
// }
// .technology-img {
//   // object-fit: contain;
//   width: 100%;
//   height: auto;
// }

// @media (max-width: 1600px) {
//   .technology-right {
//     padding-left: 44px;
//     padding-right: 30px;
//   }

//   .our-technology-section {
//     padding-top: 65px;
//     padding-bottom: 45px;
//   }
//   .technology-left {
//     display: flex;
//     margin-top: 0;
//     margin-bottom: -45px;
//     align-items: flex-end;
//   }
// }
// @media (max-width: 767px) {
//   .technology-left {
//     display: none;
//   }
//   .technology-right {
//     padding-left: 15px;
//     padding-right: 10px;
//     .main-title {
//       margin-bottom: 0;
//     }
//   }
// }
.our-technology-section {
  background-image: url("/images/technology-bg.png");
  background-repeat: no-repeat;
  background-size: cover;
  @media (max-width: 991px) {
    .technology-svg {
      max-width: 560px;
      width: 100%;
      margin: 0 auto;
    }
    .technology-right {
      max-width: 560px;
      width: 100%;
      margin: 0 auto;
    }
  }

  @media (min-width: 992px) {
    .technology-right {
      height: 372px;
      overflow: "hidden";
    }
  }
  .folder {
    background-repeat: no-repeat;
    background-size: contain;
    width: 464px;
    height: 100%;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    color: #000;
    background-color: #fff;
    position: relative;
    padding-top: 90px;
    border-radius: 20px;
    background-clip: content-box;

    &::before {
      content: "";
      background-image: url("/images/folder-top.png");
      position: absolute;
      background-position: bottom;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      height: 90px;
      background-size: contain;

      background-repeat: no-repeat;
    }

    @media (max-width: 1199px) {
      width: calc(100% - 20px);
    }
    @media (max-width: 991px) {
      width: 100%;
    }
    .technology-item-box {
      padding: 0;
      padding: 25px 40px 40px;
    @media (max-width: 991px) {
      min-height: 300px;
    }
      .srv-icon-bg {
        width: 54px;
        height: 54px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: $secondary-color;
        position: absolute;
        top: 40px;
        right: 40px;
      }
      .box-small-head {
        font-size: 24px;
        font-weight: 600;
        text-align: start;
        line-height: 42px;
        border-bottom: 4px solid $primary-color;
        margin-bottom: 15px;

        @media (max-width: 1199px) {
          font-size: 20px;
          line-height: 34px;
        }
      }
      p {
        margin-bottom: 0;
        @media (max-width: 1199px) {
          line-height: 24px;
        }
      }
    }
  }
  .swiper-wrapper {
    .swiper-pagination {
      left: 0;
      right: auto;
      height: 100%;
      padding-top: 55px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      @media (max-width: 991px) {
        flex-direction: row;
        height: auto;
        width: 100%;
        top: 0;
        align-items: flex-start;
        padding: 0;
        margin-top:25px ;
      }
      .swiper-pagination-bullet {
        flex: 1;
        width: 6px;
        border-radius: 50px;
        background-color: #c6ffd5;

        &.swiper-pagination-bullet-active {
          background-color: $secondary-color;
        }
      }
    }
    .swiper-slide {
      margin-left: 20px;
      @media (max-width: 991px) {
        margin-left: 0;
        margin-top: 50px;
      }
    }
  }
}
