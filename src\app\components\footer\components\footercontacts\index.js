import React from "react";
import Image from 'next/image';
import Link from 'next/link';


function FooterContacts() {
    const email = '<EMAIL>';
    return (
        <div className='footer-left-cont-wrap'>
            <div className='footer-cont-left'>
                <div className='footer-leftcont-row mb-60'>
                    <Link href="/"><Image
                        src="/images/beco-footer-logo.png"
                        alt="beco footer logo"
                        width={0}
                        height={0}
                        style={{ width: '188px', height: 'auto' }}
                        className="footer-logo"
                    />
                    </Link>
                </div>
                <div className='footer-leftcont-row'>
                    <span>beComap Inc. <br />14 Erb St.<br /> WWaterloo, Ontario N2L 1S7<br />(519) 594 0102</span>
                </div>
                <div className='footer-leftcont-row'>
                    <h4>Write us to</h4>
                    <div><a href={`mailto:${email}`} className="write-us-link">
                        <EMAIL>
                    </a></div>
                </div>
                <div className='footer-leftcont-row mb-24'>
                    <h4>Follow us</h4>
                    <div className="social-icons d-flex">
                        <a href="#" target="_blank" rel="noopener noreferrer">
                            <Image
                                src="/images/icons/facebook.png"
                                alt="facebook"
                                width={22}
                                height={22}
                                className="social-logo"
                            />
                        </a>
                        <a href="#" target="_blank" rel="noopener noreferrer">
                            <Image
                                src="/images/icons/linkedin.png"
                                alt="linkedin"
                                width={22}
                                height={22}
                                className="social-logo"
                            />
                        </a>
                        <a href="#" target="_blank" rel="noopener noreferrer">
                            <Image
                                src="/images/icons/twitter.png"
                                alt="twitter"
                                width={22}
                                height={22}
                                className="social-logo"
                            />
                        </a>
                        <a href="#" target="_blank" rel="noopener noreferrer">
                            <Image
                                src="/images/icons/instagram.png"
                                alt="instagram"
                                width={22}
                                height={22}
                                className="social-logo"
                            />
                        </a>
                    </div>
                </div>
                <div className='footer-leftcont-row'>
                    <p className="copyright-text">
                        © 2024 GlobeCo technologies Pvt Ltd.
                    </p>
                </div>
            </div>
        </div>
    );
}

export default FooterContacts;