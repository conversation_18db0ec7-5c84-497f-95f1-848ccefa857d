import React from 'react';
import { Container } from 'react-bootstrap';
import Button from 'react-bootstrap/Button';
import Image from 'next/image';
const Culture = () => {
    return (
        <section className='section career-culture-wrap'>
            <Container className="p-relative">
                <div className='d-flex culture-row justify-content-between align-items-center'>
                    <div className='culture-left'>
                        <h2 className="main-title mb-20">
                            Our culture
                        </h2>
                        <p className='mb-30'>
                            Our singular mission is to deliver the best technology solutions that will empower our clients to adapt to ever-changing business
                            environments easily and realize their goals. Customer focus is of paramount importance to us is embodied in the company’s culture.
                            At every step, it is about meeting the needs of our customers against any odds.nologies and extensive experience in different industry
                            verticals, we strive to deliver scalable, secure and reliable
                        </p>
                        <div className='btn-wrap'>
                            <Button className="btn-dark">Follow Us on Instagram</Button>

                        </div>
                    </div>
                    <div className='culture-right'>
                        <Image src='/images/career-culture.png' alt='career' width="420" height="420" style={{ width: '100%' }} />
                    </div>
                </div>
                <div className='culture-vector'>
                <Image src='/images/career-culture-vector.png' alt='career-vector' width="337" height="337" /> 
                </div>
            </Container>
        </section>
    );
};

export default Culture;
