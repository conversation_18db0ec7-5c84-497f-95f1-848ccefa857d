import React, { useEffect, useState } from 'react';
import BlogSubscribe from '../subscribe';




const BlogDetailContent = ({ content }) => {


 

  // Function to create nested TOC structure
 

  

  const renderContentWithSubscription = () => {
    const paragraphs = content.split(/<\/p>/).map((para, index) => {
      return (
        <div key={index}>
          <div dangerouslySetInnerHTML={{ __html: para + '</p>' }} />
          {index === 0 && <BlogSubscribe />}
        </div>
      );
    });

    return paragraphs;
  };

  return (
    <div className='blog-detail-content'>
      {renderContentWithSubscription()}
    </div>
  );
};

export default BlogDetailContent;
