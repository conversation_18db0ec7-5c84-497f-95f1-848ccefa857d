.thanks-sec {
  .header-tha {
    background-color: #ffb82d;
    padding: 110px 0;
    text-align: center;
    padding-bottom: 200px;
    position: relative;
    z-index: 1;
    h1 {
      font-family: $font-bold;
      font-weight: 900;
      font-size: 48px;
      position: relative;
      z-index: 2;
    }
    p {
      font-size: 20px;
      font-weight: 500;
      margin: 0;
      position: relative;
      z-index: 2;
    }
  }
  .footer-tha {
    position: relative;
    z-index: 2;
    background-image: url("/images/thanks-bg.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    padding: 50px 0 100px;
    .footer-card {
      background-color: #fff;
      padding: 30px;
      border-radius: 30px;
    margin-top: -180px;

      .thank-links {
        padding: 28px 80px;
        border: 2px solid #000000;
        border-radius: 28px;
        h2 {
          font-size: 24px;
          font-weight: 700;
          font-family: $font-bold;
          text-align: center;
        }
        p {
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
          text-align: center;
          margin-bottom: 30px;
        }
        .links-wrap {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 20px;
          flex-wrap: wrap;
          margin: 0;
          padding: 0;
          li {
            list-style: none;
            a {
              text-decoration: none;
              font-size: 15px;
              font-weight: 500;
              line-height: 12px;
              text-align: left;
              color: #000;
              display: flex;
              align-items: center;
              justify-content: center;
              font-family: $font-medium;
              gap: 10px;
              padding: 15px;
              border-radius: 50px;
            }
            &:nth-of-type(odd) {
              a {
                border: 2px solid $primary-color;
              }
            }
            &:nth-of-type(even) {
              a {
                border: 2px solid $secondary-color;
              }
            }
          }
        }
      }
      .thank-blog {
        border: 2px solid #34a853;
        padding: 25px 30px;
        border-radius: 28px;
        margin-top: 20px;
        h2 {
          font-size: 20px;
          font-weight: 700;
          font-family: $font-bold;
          line-height: 15px;
          text-align: left;
          margin-bottom: 15px;
        }
        p {
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
          text-align: left;
          margin-bottom: 30px;
        }
        button {
          margin-top: 20px;
          border: 2px solid #34a853;
        }
      }
      .thank-ebook {
        border: 2px solid #2f2f2f;
        background: #2f2f2f;
        padding: 25px 30px;
        border-radius: 28px;
        margin-top: 20px;
        text-align: center;
        color: #fff;
        h2 {
          font-family: $font-bold;
          text-align: left;
          font-size: 20px;
          font-weight: 700;
          line-height: 15px;
          text-align: left;
          margin-bottom: 11px;
        }
        p {
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
          text-align: left;
          margin-bottom: 30px;
        }
        button {
          margin-top: 20px;
          width: 100%;
        }
      }
    }
  }
}
