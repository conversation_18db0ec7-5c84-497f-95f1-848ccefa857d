"use client";
import Image from "next/image";
import { Container, Row, Col } from "react-bootstrap";

const ServiceContent = ({ data, isReversed }) => (
  <div
    className={`service-item-content ${
      isReversed ? "ms-5 ps-5 text-start" : "me-5 pe-5 text-start"
    }`}
  >
    <div
      className={`service-icon-wrap d-flex flex-column ${
        isReversed ? "align-items-start" : "align-items-start"
      }`}
    >
      {data.icon && (
        <span className="srv-icon">
          <Image
            src={data.icon}
            alt={data.title}
            width={30}
            height={30}
            className="solution-icon-img"
          />
        </span>
      )}
      <h4 className="small-head">{data.title}</h4>
    </div>
    <p className="mb-0">{data.content}</p>
  </div>
);

const IndustryServices = ({ title, description, serviceData, image }) => {
  return (
    <section className={`section p-0 service-section overflow-hidden`}>
      <Container>
        <Row className="align-items-center pt-5 pb-4">
          <Col xs={7}>
            <div className="im-wrapper">
              <Image
                src={image}
                alt="banner-image"
                width="452"
                height="368"
                style={{ width: "100%", height: "auto" }}
              />
            </div>
          </Col>
          <Col xs={5}>
            <div className="soultion-details px-0">
              <h2 className="main-title mb-3">{title}</h2>
              <p
                className="mb-0"
                style={{
                  paddingRight: "9%",
                }}
              >
                {description}
              </p>
            </div>
          </Col>
        </Row>
      </Container>
      <Container fluid style={{ padding: "0px" }}>
        {/* <div className="company-value-head">
          <h2 className="main-title">{title}</h2>
          <p>{description}</p>
        </div> */}
        {serviceData.map((service, index) => {
          const isReversed = index % 2 !== 0;
          return (
            <Row
              key={index}
              className={`align-items-center g-0 ${
                isReversed ? "justify-content-start" : "justify-content-end"
              }`}
              style={{ backgroundColor: isReversed ? "#ffffff" : "#f3f3f3" }}
            >
              {isReversed ? (
                <>
                  <Col xs={6}>
                    <div className="im-wrapper">
                      <Image
                        src={service.image}
                        alt={service.title}
                        width="452"
                        height="368"
                        style={{ width: "100%", height: "auto" }}
                      />
                    </div>
                  </Col>
                  <Col xs={{ span: 4 }}>
                    <ServiceContent data={service} isReversed={isReversed} />
                  </Col>
                </>
              ) : (
                <>
                  <Col xs={{ span: 4, offset: 1 }}>
                    <ServiceContent data={service} isReversed={isReversed} />
                  </Col>
                  <Col xs={6}>
                    <div className="im-wrapper">
                      <Image
                        src={service.image}
                        alt={service.title}
                        width="452"
                        height="368"
                        style={{ width: "100%", height: "auto" }}
                      />
                    </div>
                  </Col>
                </>
              )}
            </Row>
          );
        })}
      </Container>
    </section>
  );
};

export default IndustryServices;
