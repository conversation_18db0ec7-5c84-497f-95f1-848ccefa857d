.mapping-partner-left {
  width: 50%;
  background-image: url("/images/mapping-sec-leftbg.png");
  background-size: cover;
  background-position: right;
  background-color: #ededed;
}
.mapping-partner-row {
  background-color: $primary-color;
}
.mapping-partner-right {
  padding: 91px 0 50px 94px;
  max-width: 617px;
  p {
    margin-bottom: 40px;
  }
}
.mapping-vector-bg {
  background-image: url("/images/mapping-partner-bg.png");
  background-size: cover;
  background-position: right;
  position: absolute;
  width: 272px;
  top: 0;
  bottom: 0;
  right: 0;
  pointer-events: none;
}
@media (max-width: 1400px) {
  .mapping-partner-left {
    background-position: center;
  }
  .mapping-partner-right {
    padding: 60px 20px 30px 30px;
    max-width: 700px;
  }
}
@media (max-width: 767px) {
  .mapping-partner-row {
    flex-direction: column;
  }
  .mapping-partner-left {
    width: 100%;
    height: 300px;
  }
  .mapping-vector-bg {
    display: none;
  }
  .mapping-partner-right {
    padding: 20px 20px 0;
    max-width: 100%;
  }
}

.mapping-partner-section {
  padding-top: 0;

  @media (max-width: 1399px) {
    .main-title {
      font-size: 34px;
      line-height: 43px;
      margin-bottom: 20px !important;
    }
  }

  @media (max-width: 1199px) {
    .main-title {
      margin-bottom: 12px !important;
    }
    .main-desc {
      line-height: 26px;
    }
  }
  @media (max-width: 991px) {
    padding: 0px 30px;
    .main-title {
      padding: 0 15%;
      text-align: center;
    }
    .main-desc {
      padding: 0 15%;
      text-align: center;
    }
  }
  @media (max-width: 767px) {
    padding: 0px 20px;

    .main-title {
      font-size: 26px;
      line-height: 32px;
      padding: 0;
      text-align: center;
    }
    .main-desc {
        padding: 0;
      text-align: center;
    }
  }
}
