"use client";
import Image from "next/image";
import BlogEbook from "../ebook";
import {
  FacebookShareButton,
  TelegramShareButton,
  TwitterShareButton,
  LinkedinShareButton,
  WhatsappShareButton,
} from "next-share";

// import {
//   FacebookShareButton,
//   ,
//   TwitterShareButton,
//   WhatsAppShareButton,
//   TelegramShareButton,
// } from "next-share";

const BlogDetailsLeft = ({ title, tableOfContent }) => {
  const renderToc = (toc) => {
    return toc.map((item) => (
      <li key={item.id}>
        <div
          onClick={(e) => {
            e.preventDefault();
            scrollToSection(item.id);
          }}
        >
          {item.text}
        </div>
        {item.children && item.children.length > 0 && (
          <ul>{renderToc(item.children)}</ul>
        )}
      </li>
    ));
  };

  // Function to scroll to the section without changing the URL
  const scrollToSection = (id) => {
    const section = document.getElementById(id);
    if (section) {
      section.scrollIntoView({ behavior: "smooth" });
    }
  };

  const ebookContent = {
    imageSrc: "/images/ebook.png",
    title:
      "The Role of Infrared Sensor Technology in Creating Accurate Indoor Maps",
    // subtitle: "Strategies for Successful Implementation and Deployment",
    paragraphContent:
      "Explore how AI transforms indoor maps, enhancing navigation accuracy and user experience. Discover the future of AI-driven mapping solutions...",
  };
  return (
    <div className="blog-details-left-wrap">
      {tableOfContent.length > 0 && (
        <div className="table-of-contents">
          <h4>Table of Contents</h4>
          <ol className="tableof-content-list">{renderToc(tableOfContent)}</ol>
        </div>
      )}
      {/* 
                
                <ol className='tableof-content-list'>
                    
                    <li>Indoor Mapping Accuracy</li>
                    <li>Understanding Infrared Sensor Technology</li>
                    <li>Indoor Mapping Accuracy</li>
                    <li>Understanding Infrared Sensor Technology</li>
                    <li>Indoor Mapping Accuracy</li>
                    <li>Understanding Infrared Sensor Technology</li>
                </ol>
            </div> */}
      <div className="blog-social-box-wrap mb-30">
        <h4 className="mb-3">Share This Article</h4>
        <div className="blog-social-box">
          <FacebookShareButton url={window.location.href} quote={title}>
            <Image
              src="/images/icons/blog-f.png"
              alt="facebook"
              width={34}
              height={34}
              className="social-logo"
            />
          </FacebookShareButton>
          <LinkedinShareButton url={window.location.href}>
            <Image
              src="/images/icons/blog-in.png"
              alt="linkedin"
              width={34}
              height={34}
              className="social-logo"
            />
          </LinkedinShareButton>
          <TwitterShareButton url={window.location.href} title={title}>
            <Image
              src="/images/icons/blog-x.png"
              alt="twitter"
              width={34}
              height={34}
              className="social-logo"
            />
          </TwitterShareButton>
          <WhatsappShareButton url={window.location.href} title={title}>
            <Image
              src="/images/icons/blog-wh.png"
              alt="whatsapp"
              width={34}
              height={34}
              className="social-logo"
            />
          </WhatsappShareButton>
          <TelegramShareButton url={window.location.href} title={title}>
            <Image
              src="/images/icons/blog-inst.png"
              alt="telegram"
              width={34}
              height={34}
              className="social-logo"
            />
          </TelegramShareButton>
        </div>
      </div>
      <div className="detail-ebook-box">
        {/* <BlogEbook {...ebookContent} /> */}
      </div>
    </div>
  );
};

export default BlogDetailsLeft;
