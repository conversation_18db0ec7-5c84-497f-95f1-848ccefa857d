import ClientSlider from "@/app/company/_components/companybanner/components/clients";
import Image from "next/image";
import { Col, Container, Row } from "react-bootstrap";

const IndustryExperience = ({ title, image, description }) => {
  return (
    <section className="section ex-section bg-bc-light px-3 px-xl-0">
      <Container fluid={"xl"}>
        <Row className="align-items-center gx-lg-5">
          <Col xl={5} lg={6}>
            <div className="soultion-details mb-lg-0 mb-4">
              <h2 className="main-title mb-3">{title}</h2>
              <p className="mb-0">{description}</p>
            </div>
          </Col>
          <Col xl={7} lg={6}>
            <div className="im-wrapper ms-xl-5 ms-0">
              <Image
                src={image.src}
                alt="banner-image"
                width="452"
                height="368"
                style={{ width: "100%", height: "auto" }}
              />
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default IndustryExperience;
