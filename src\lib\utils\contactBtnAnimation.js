import confetti from "canvas-confetti";

export const startConfetti = (buttonRef, textRef) => {
  const text = textRef.current;
  const button = buttonRef.current;

  if (!text || !button) return; // Guard clause to ensure refs are valid

  text.textContent = "";
  button.classList.add("loading");

  const rect = button.getBoundingClientRect();
  const center = {
    x: rect.left + rect.width / 2,
    y: rect.top + rect.height / 2,
  };
  const origin = {
    x: center.x / window.innerWidth,
    y: center.y / window.innerHeight,
  };

  // Canvas and confetti settings
  const myCanvas = document.createElement("canvas");
  document.body.appendChild(myCanvas);
  const defaults = {
    disableForReducedMotion: true,
  };
  const colors = ["#FFB82D", "#34a853", "#000000"]; // Fixed colors for confetti

  // Confetti function to be more realistic
  function fire(particleRatio, opts) {
    confetti(
      Object.assign({}, defaults, opts, {
        particleCount: Math.floor(100 * particleRatio),
        colors, // Use the fixed colors
      })
    );
  }

  // Finished state confetti
  setTimeout(() => {
    button.classList.remove("loading");
    button.classList.add("success");
    fire(0.25, {
      spread: 26,
      startVelocity: 10,
      origin,
      colors,
    });
    fire(0.2, {
      spread: 60,
      startVelocity: 20,
      origin,
      colors,
    });
    fire(0.35, {
      spread: 100,
      startVelocity: 15,
      decay: 0.91,
      origin,
      colors,
    });
    fire(0.1, {
      spread: 120,
      startVelocity: 10,
      decay: 0.92,
      origin,
      colors,
    });
    fire(0.1, {
      spread: 120,
      startVelocity: 20,
      origin,
      colors,
    });
  }, 3000);

  // Finished state text
  setTimeout(() => {
    text.textContent = "Submitted";
  }, 3500);

  // Reset animation
  setTimeout(() => {
    text.textContent = "Talk to our Team";
    button.classList.remove("loading");
  }, 6000);
};
