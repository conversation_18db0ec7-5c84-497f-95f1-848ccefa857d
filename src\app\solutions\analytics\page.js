import ClientSlider from "@/app/company/_components/companybanner/components/clients";
import SolutionBanner from "../_components/Banner";
import IndustryClients from "../_components/IndustryClients";
// import IndustryExperience from "../_components/IndustryExperience";
// import IndustrySecondSection from "../_components/IndustrySecondSection";
import IndustrySolutions from "../_components/solutions";
// import IndustryAccordion from "../_components/IndustryAccordion";
import IndustryServices from "../_components/IndustryServices";
import IndustryEBook from "../_components/IndustryEBook";
import FaqSection from "../_components/FaqSection";
import SolutionWhyUs from "../_components/solutionWhyUs";
import HeroImg from "@/app/assets/images/solutions/analytics-hero.png";
import SecImg from "@/app/assets/images/solutions/becomap-care.png";
// import SecImg2 from "@/app/assets/images/industry/mall-sec-im-2.png";
// import SecImg3 from "@/app/assets/images/industry/mall-help-illu.png";
// import SecImg4 from "@/app/assets/images/industry/mall-im-sec-2-2.png";
// import BgImg from "@/app/assets/images/industry/indu-accr-bg.png";
import TalkToOurTeam from "@/app/components/talktoourteam";
import Blogs from "@/app/home/<USER>/blogs";
import { Col, Container, Row } from "react-bootstrap";
import Image from "next/image";

import SolutionWhyUsBg from "@/app/assets/images/solutions/why-bg.png";
import WhySubImage from "@/app/assets/images/solutions/analytics/why-sub-img.png";
import SolutionsServices from "../_components/IndustryServices";


export const metadata = {
  metadataBase: new URL("https://becomap.com"),
  title: "Indoor Analytics service | Becomap",
  description:
    "Unlock the power of Indoor Analytics with Becomap's solutions. Enhance location-based insights, improve user experience, and optimize indoor spaces effectively.",
  images: ["/becomap.png"],
  openGraph: {
    title: "Indoor Analytics service | Becomap",
    description:
      "Unlock the power of Indoor Analytics with Becomap's solutions. Enhance location-based insights, improve user experience, and optimize indoor spaces effectively.",
    images: ["/becomap.png"],
  },
};



const mallNavigationFAQ = [
  {
    question: "How can analytics help improve customer experience?",
    answer: "Analytics helps businesses understand customer preferences, behavior patterns, and pain points, enabling personalized services, targeted marketing campaigns, and enhanced customer satisfaction."
  },
  {
    question: "What is analytics, and how can it benefit my business?",
    answer: "Analytics involves collecting, processing, and analyzing data to gain insights into business performance, customer behavior, and operational efficiency. It helps businesses make data-driven decisions, improve strategies, and optimize processes."
  },
  {
    question: "Is the data collected through analytics secure?",
    answer: "Yes, we implement industry-standard security measures, including data encryption, secure storage, and access controls, to protect your business and customer data from unauthorized access or breaches."
  }
];

const SOLUTIONS = [
  {
    title: "Visitor Journey Analysis",
    description:
      "Track how visitors navigate through your space. Identify popular routes, high-traffic zones, and underused areas to make informed decisions about layout changes, marketing strategies, or service placement.",
    iconUrl: "/images/icons/route.png",
    color: "#34A853",
  },
  {
    title: "Performance Monitoring",
    description:
      "Monitor key performance indicators such as peak usage times, asset movement, and operational bottlenecks. With detailed data, you can optimize workflows, reduce inefficiencies, and improve asset utilization.",
    iconUrl: "/images/icons/dashboard.png",
    color: "#34A853",
  },
  {
    title: "Customizable Dashboards and Reports",
    description:
      "beComap’s Analytics offers pre-built dashboards that display key metrics. You can filter data by time period or venue, view charts, and export reports to share insights across your organization, helping your team stay aligned on goals and strategies.",
    iconUrl: "/images/icons/monitor.png",
    color: "#34A853",
  },
];

const accordionData = [
  {
    title: "Easily Integrate Maps",
    content:
      "Integrate map of your retail space into existing apps to effortlessly guide shoppers to their favorite stores, restaurants, and services via the shortest route.",
  },
  {
    title: "Multi-Language Support",
    content:
      "Make your mall map accessible to all visitors by offering it in multiple languages. beComap allows you to provide directions and information in the shopper’s preferred language, making the mall experience more personalized and user-friendly. This feature is particularly valuable in diverse or tourist-heavy locations.",
  },
  {
    title: "Give Updates on Store Offers",
    content:
      "Keep shoppers in the loop with instant updates on store deals, upcoming events, and essential services, improving their overall shopping experience.",
  },
  {
    title: "Less Searching, More Shopping",
    content:
      "Navigate in a flash with our system—find the fastest route and shortest path from parking to stores, or multi-stop routes to plan your shopping with less walking and more items on your cart.",
  },
];

const ServiceData = [
  {
    title: "1. Install Data Collection Tools",
    content:
      "We begin by installing bluetooth beacons and data-gathering devices across your facility. These tools track visitor flow, asset movement, and operational performance to ensure you have all the necessary information for improving your facility.",
    image: "/images/install-data-collection-tools.png",
  },
  {
    title: "2. Analyze Collected Data",
    content:
      "After collecting the data, our system processes it and generates insights based on key metrics. You can easily view these insights through our customizable dashboards, giving you a clear picture of how your facility operates.",
    image: "/images/analyze-collected-data.png",
  },
  {
    title: "3. Implement Data-Driven Changes",
    content:
      "With actionable insights in hand, you can start making informed decisions about visitor engagement, layout optimization, and resource management. Whether it's adjusting layouts or boosting marketing efforts, beComap’s Analytics helps you make data-driven improvements.",
    image: "/images/develop-the-kiosk-interface.png",
  },
];

const WHY_DATA = [
  {
    title: "Data Collection",
    description:
      "We collect data using Bluetooth beacons placed throughout your indoor space. This data reveals visitor flow, asset locations, and high-traffic areas, providing valuable insights for better analysis and decision-making.",
    iconUrl: "/images/icons/beacon.png",
    activeIconUrl: "/images/icons/beacon-active.png",
    color: "#34A853",
  },
  {
    title: "Data Processing",
    description:
      "Once collected, the data is processed through our platform. Key metrics, such as visitor paths, dwell times, and location-based interactions, are visualized on a user-friendly dashboard for easy review.",
    iconUrl: "/images/icons/science.png",
    activeIconUrl: "/images/icons/science-active.png",
    color: "#34A853",
  },
  {
    title: "Actionable Insights",
    description:
      "With beComap’s Analytics, you gain insights that help you improve your facility’s layout, resource allocation, and marketing strategies. These insights allow you to improve visitor experience, drive engagement, and optimize your operations.",
    iconUrl: "/images/icons/statistics.png",
    activeIconUrl: "/images/icons/statistics-active.png",
    color: "#34A853",
  },
];

const Kiosk = () => {
  return (
    <div className="contact-wrap" style={{ padding: 0 }}>
      <SolutionBanner
        source={"analytics"}
        bgImage={HeroImg}
        title="Unlock Insights with Our Advanced Indoor Analytics Service"
        description="beComap’s Analytics collects and analyzes data on visitor movement, asset usage, and facility performance. By translating this data into actionable insights, you can optimize your facility, increase engagement, and improve operational strategies based on actual visitor behavior."
      />
      <IndustryClients />
      <SolutionWhyUs
        title={"What is beComap Analytics?"}
        description={
          "beComap Analytics helps you understand how visitors interact with your space by tracking movements, usage patterns, and key metrics. This data provides valuable insights into visitor behavior, allowing you to optimize layouts, improve operations, and make informed decisions to create a more engaging and efficient environment for everyone."
        }
        bgImage={SolutionWhyUsBg}
        subImage={WhySubImage}
        whyData={WHY_DATA}
      />
      <IndustrySolutions
        image={SecImg}
        solutions={SOLUTIONS}
        title={"How does beComap’s Analytics Work?"}
        description={{
          part1:
            "beComap’s Analytics provides valuable insights into customer behavior and preferences, helping you refine your operations for maximum impact. By analyzing visitor movement, dwell times, and high-traffic areas, you can optimize layouts and improve resource allocation across your space. This data-driven approach allows you to create targeted promotions, adjust store placements, and ",
          part2:
            "develop marketing strategies that resonate with your audience.",
          part3:
            "With detailed analytics, you gain a clearer understanding of what attracts visitors and how to enhance their experience. Experiment with different layouts, signage, or promotions to discover what works best. These insights improve the visitor experience and support smarter choices to drive foot traffic and increase sales. beComap’s analytics tools transform data into actionable insights, helping you build a more engaging and efficient environment.",
        }}
      />

      {/* 
      <IndustrySecondSection
        image={SecImg4}
        routeType="shoppingmall"
        title={"No More Mazes. Get Shoppers Straight to the stores."}
        description="Lost customers and missed sales opportunities are things of the past.  beComap's Retail Wayfinding Solution is here to change that. Our system turns confusion into clarity, allowing your mall visitors to navigate your store with ease. They can quickly find what they’re looking for, explore the latest deals, and stay informed about any changes—all without frustration. With beComap, your customers enjoy a continuous shopping experience, and you benefit from happier clients and increased foot traffic and sales."
      />
      <IndustrySolutions
        imageProperty={{
          width: 441,
          height: 505,
          top: "-120px",
          bottom: "0",
          justifyContentParent: "end",
          justifyContent: "end",
          backgroundColor: "gradient",
        }}
        image={SecImg}
        solutions={SOLUTIONS}
        title={"Mall Experience like never before"}
        description="Lost customers and missed sales opportunities are things of the past.  beComap's Retail Wayfinding Solution is here to change that. Our system turns confusion into clarity, allowing your mall visitors to navigate your store with ease. They can quickly find what they’re looking for, explore the latest deals, and stay informed about any changes—all without frustration. With beComap, your customers enjoy a continuous shopping experience, and you benefit from happier clients and increased foot traffic and sales."
      />
      <IndustryExperience
        image={SecImg2}
        title="Mall Navigation Made Easy"
        description="Our solution makes navigating your mall simple and stress-free. It offers detailed info on essential services like restrooms, ATMs, and customer service desks through the app. Shoppers can use easy turn-by-turn directions to find stores and services quickly. By enhancing their overall experience and reducing frustration, more visitors will be encouraged to spend time in our mall, increasing foot traffic and driving sales."
      />
      <IndustryAccordion
        title={"Discover 3X More Stores!"}
        bgImage={BgImg}
        description={
          "With beComap, shoppers get a complete list of all the stores in your mall, including detailed information and current offers. Now, they can explore more shops than ever before and find the best deals, turning every visit into an exciting shopping adventure and driving more foot traffic to your mall."
        }
        accordionData={accordionData}
        videoSrc={"/videos/mall.mp4"}
      />
    
      <IndustryHelp
        title="Why World’s largest venues trust beComap"
        description="beComap changes the way you think about retail navigation & engagement. Our platform simplifies navigation, attracts and retains customers, and optimizes operations to improve the overall shopping journey."
        image={SecImg3}
        points={[
          "Best-in-class indoor mapping for malls",
          "8 years on active R&D in location intelligence",
          "Optimized for use on mobile devices, web browsers, and kiosks",
          "Easy integration with existing apps",
          "Web-based navigation without any downloads",
          "Multiple language support",
        ]}
      />
 */}

      <SolutionsServices
        title="How We Set Up Analytics for Your Facility"
        description="Setting up beComap’s Analytics for your facility is simple. We start by installing sensors and devices that track visitor movements, asset usage, and overall performance. This data is processed and presented in customizable dashboards, allowing you to easily view key metrics. With this information, you can make informed decisions about improving layouts, enhancing visitor engagement, and managing resources more effectively. The insights you gain help you optimize your facility’s operations, ensuring everything runs smoothly and efficiently."
        image={"/images/kiosk-dummy.png"}
        serviceData={ServiceData}
      />
      <section
        className="section primary-bg"
        style={{
          backgroundImage: `url("/images/solution-bg.png")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          minHeight: "680px",
        }}
      >
        <Container>
          <Row className="align-items-center">
            <Col lg={7}>
              <Image
                src={"/images/solution-sec.png"}
                alt="indoor mapping software"
                width={550}
                height={450}
                style={{ width: "100%", height: "auto" }}
              />
            </Col>
            <Col lg={5}>
              <div className="section-head">
                <span
                  className="mb-4"
                  style={{
                    fontWeight: 600,
                    fontSize: "18px",
                  }}
                >
                  Case Study
                </span>
                <h2 className="main-title mb-3">Indoor mapping software</h2>
                <p>
                  Develop an ultra-modern travel environment that ree
                  experiences. Set new standards in travel convenience and
                  satisfaction through innovation, sustainability, and
                  operational efficiency.
                </p>
              </div>
              <button className="btn btn-dark mt-4">Learn More</button>
            </Col>
          </Row>
        </Container>
      </section>
      {/* <IndustryEBook
        image={"/images/mall-ebook-bg.png"}
        title={"Retail In Action"}
        description={`<p>According to ACI, global passenger traffic will reach 9.7 billion this year.</p> <p>Learn how to deliver a smooth travel experience with indoor maps in our helpful retail guide.</p>`}
      /> */}
      <Blogs />
      <TalkToOurTeam source={"analytics"} />
      <FaqSection
        title={"Frequently Asked Questions"}
        accordionData={mallNavigationFAQ}
      />
    </div>
  );
};

export default Kiosk;
