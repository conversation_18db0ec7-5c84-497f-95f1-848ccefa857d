import axios from "axios";
export async function generateMetadata({ params }, parent) {
    const url = params.url
    const response = await axios.get(`https://blog-api.becomap.com/posts/${url}`, {
        headers: {
            'X-Blog-Identifier': 'beco',
        },
    });

    const blog = response.data
    return {
        metadataBase: new URL('https://becomap.com'),
        title: blog.meta_title,
        description: blog.meta_description,
        images: [blog.image],
        openGraph: {
            title: blog.meta_title,
            images: [blog.image],
            description: blog.meta_description,
        },
        alternates: {
            canonical: `/blog/${blog.url}/`,
        }
    };
}

const PostLayout = ({ children }) => {
    return (
        <>
            {children}
        </>
    );
}

export default PostLayout;