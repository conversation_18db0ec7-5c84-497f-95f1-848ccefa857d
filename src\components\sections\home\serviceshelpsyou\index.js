"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import Container from "react-bootstrap/Container";
import ServicesHelpsSlider1 from "./_components/slide1";
import { Navigation } from "swiper/modules";
import ServicesHelpsSlider2 from "./_components/slide2";

const ServicesHelpsYou = () => {
  return (
    <div className="section services-helpsyou-section">
      <Container fluid={"xl"}>
        <div className="section-head text-center">
          <h2 className="main-title mb-60">
            Services That Will Help You Build a Better Business
          </h2>
        </div>
        <div className="srv-helpslide-wrap">
          <div className="srv-helpslide-box d-flex">
            <div className="srv-helpslide-box-left">
              <div className="srv-count-box">
                <h2>900+</h2>
                <div className="srv-count-title primary-bg">Maps</div>
              </div>
              <div className="srv-count-box">
                <h2>1.5+</h2>
                <div className="srv-count-title secondary-bg">Billion sqft</div>
              </div>
            </div>
            <Swiper
              slidesPerView={1}
              spaceBetween={30}
              className="srv-help-slider"
              loop={true}
              navigation={{
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
              }}
              modules={[Navigation]}
              autoplay={{
                delay: 200,
                disableOnInteraction: false,
              }}
            >
              <SwiperSlide>
                <ServicesHelpsSlider1 />
              </SwiperSlide>
              <SwiperSlide>
                <ServicesHelpsSlider2 />
              </SwiperSlide>
            </Swiper>
            <div className="swiper-button-prev"></div>
            <div className="swiper-button-next"></div>
            <div className="srv-helpslide-box-right">
              <div className="srv-slide-content">
                At beComap, we provide powerful indoor navigation software
                andexperiences and streamline operainding to minimize confusion.
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default ServicesHelpsYou;
