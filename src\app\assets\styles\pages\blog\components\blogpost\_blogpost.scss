.blog-postimage-wrap {
    margin-bottom: 23px;
   
    img {
        border-radius: 20px;
        object-fit: cover;
    }
}
.blogpost-topic-info {
    margin: 0 -8px 10px;
    span {
        font-family: $font-bold;
        padding: 0 8px;
        position: relative;
        line-height: 20px;
        &:first-child::after {
            position: absolute;
            content: "";
            width: 2px;
            background-color: black;
            top: 2px;
            bottom: 2px;
            right: 0;
        }
    }

    .blogpost-topic {
        color: $primary-color;
    }
}
.blog-post-description {
    .blogpost-heading {
        font-size: 30px;
        line-height: 40px;
        font-family: $font-bold;
    }
    .blogpost-paragraph {
        font-size: 14px;
        margin: 0;
        color: rgba(105, 105, 105, 1);
        line-height: 22px;
    }
}
.blogpost {
    margin-bottom: 30px;
    .blogpost-ins {
        text-decoration: none;
        color: inherit;
    }
}
